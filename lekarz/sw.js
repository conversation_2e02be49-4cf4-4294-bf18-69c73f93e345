const CACHE_NAME = 'doctor-panel-v1';

// Instalacja Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker installed');
    self.skipWaiting();
});

// Aktywacja Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker activated');
    event.waitUntil(self.clients.claim());
});

// Interceptowanie żądań - aplikacja działa tylko online
self.addEventListener('fetch', event => {
    // Dla żądań API - zawsze pobieraj z sieci
    if (event.request.url.includes('/api/')) {
        event.respondWith(
            fetch(event.request)
                .catch(error => {
                    console.log('Network error:', error);
                    // Zwróć błąd połączenia
                    return new Response(JSON.stringify({
                        error: 'Brak połączenia z serwerem',
                        offline: true
                    }), {
                        status: 503,
                        headers: { 'Content-Type': 'application/json' }
                    });
                })
        );
        return;
    }

    // Dla innych zasobów - pobieraj z sieci, ale cache'uj
    event.respondWith(
        fetch(event.request)
            .then(response => {
                // Cache'uj tylko statyczne zasoby
                if (event.request.method === 'GET' &&
                    (event.request.url.includes('.css') ||
                        event.request.url.includes('.js') ||
                        event.request.url.includes('.html'))) {
                    const responseClone = response.clone();
                    caches.open(CACHE_NAME).then(cache => {
                        cache.put(event.request, responseClone);
                    });
                }
                return response;
            })
            .catch(error => {
                console.log('Fetch failed:', error);
                // Spróbuj pobrać z cache jako fallback
                return caches.match(event.request);
            })
    );
});

// Obsługa powiadomień push (opcjonalnie)
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        const options = {
            body: data.body,
            vibrate: [100, 50, 100],
            data: {
                dateOfArrival: Date.now(),
                primaryKey: 1
            },
            actions: [
                {
                    action: 'explore',
                    title: 'Otwórz aplikację'
                },
                {
                    action: 'close',
                    title: 'Zamknij'
                }
            ]
        };

        event.waitUntil(
            self.registration.showNotification(data.title, options)
        );
    }
});

// Obsługa kliknięć w powiadomienia
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/lekarz/')
        );
    }
}); 