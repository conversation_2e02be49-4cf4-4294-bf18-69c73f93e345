/**
 * Cache Client - Klient do obsługi systemu cache dla PWA
 */

class CacheClient {
    constructor(apiBaseUrl = '/api/v2') {
        this.apiBaseUrl = apiBaseUrl;
        this.lastSyncTimestamps = this.loadLastSyncTimestamps();
    }

    /**
     * Ładowanie timestampów ostatniej synchronizacji z localStorage
     * @returns {Object} Timestampy ostatniej synchronizacji
     */
    loadLastSyncTimestamps() {
        const storedTimestamps = localStorage.getItem('lastSyncTimestamps');
        if (storedTimestamps) {
            try {
                return JSON.parse(storedTimestamps);
            } catch (e) {
                console.error('Błąd podczas parsowania timestampów:', e);
            }
        }
        return {
            appointments: 0,
            doctors: 0,
            clients: 0,
            rooms: 0,
            queue: 0,
            categories: 0,
            campaigns: 0,
            users: 0
        };
    }

    /**
     * Zapisanie timestampów ostatniej synchronizacji do localStorage
     * @param {Object} timestamps Timestampy do zapisania
     */
    saveLastSyncTimestamps(timestamps) {
        localStorage.setItem('lastSyncTimestamps', JSON.stringify(timestamps));
        this.lastSyncTimestamps = timestamps;
    }

    /**
     * Aktualizacja timestampów ostatniej synchronizacji na podstawie zmian
     * @param {Object} changes Zmiany zwrócone przez API
     */
    updateLastSyncTimestamps(changes) {
        const timestamps = { ...this.lastSyncTimestamps };
        
        Object.keys(changes).forEach(entityType => {
            if (changes[entityType] && changes[entityType].lastUpdate) {
                timestamps[entityType] = changes[entityType].lastUpdate;
            }
        });
        
        this.saveLastSyncTimestamps(timestamps);
    }

    /**
     * Sprawdzenie zmian w cache
     * @param {string} clientId ID klienta
     * @returns {Promise<Object>} Informacje o zmianach
     */
    async checkChanges(clientId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/cache/changes/${clientId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ lastSync: this.lastSyncTimestamps })
            });
            
            if (!response.ok) {
                throw new Error(`Błąd HTTP: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message || 'Nieznany błąd');
            }
        } catch (error) {
            console.error('Błąd podczas sprawdzania zmian:', error);
            throw error;
        }
    }

    /**
     * Sprawdzenie zmian i pobranie zaktualizowanych danych
     * @param {string} clientId ID klienta
     * @param {Object} fetchCallbacks Funkcje do pobierania danych dla poszczególnych typów
     * @returns {Promise<Object>} Informacje o pobranych danych
     */
    async checkAndFetchUpdates(clientId, fetchCallbacks = {}) {
        try {
            const changes = await this.checkChanges(clientId);
            const results = {
                hasChanges: changes.hasChanges,
                fetchedData: {},
                changes: changes.changes
            };
            
            // Jeśli są zmiany, pobierz dane
            if (changes.hasChanges) {
                const fetchPromises = [];
                
                // Dla każdego typu danych, który ma zmiany
                Object.keys(changes.changes).forEach(entityType => {
                    const entityChanges = changes.changes[entityType];
                    
                    if (entityChanges.hasChanges && fetchCallbacks[entityType]) {
                        // Dodaj obietnicę pobrania danych
                        fetchPromises.push(
                            fetchCallbacks[entityType]()
                                .then(data => {
                                    results.fetchedData[entityType] = data;
                                    return data;
                                })
                        );
                    }
                });
                
                // Poczekaj na wszystkie operacje pobrania
                await Promise.all(fetchPromises);
                
                // Zaktualizuj timestampy ostatniej synchronizacji
                this.updateLastSyncTimestamps(changes.changes);
            }
            
            return results;
        } catch (error) {
            console.error('Błąd podczas sprawdzania i pobierania aktualizacji:', error);
            throw error;
        }
    }

    /**
     * Pobranie wszystkich timestampów zmian
     * @returns {Promise<Object>} Timestampy zmian
     */
    async getTimestamps() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/cache/timestamps`);
            
            if (!response.ok) {
                throw new Error(`Błąd HTTP: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                return data.data.timestamps;
            } else {
                throw new Error(data.message || 'Nieznany błąd');
            }
        } catch (error) {
            console.error('Błąd podczas pobierania timestampów:', error);
            throw error;
        }
    }

    /**
     * Resetowanie timestampów ostatniej synchronizacji
     * Wymusza pobranie wszystkich danych przy następnym sprawdzeniu zmian
     */
    resetLastSyncTimestamps() {
        const resetTimestamps = {
            appointments: 0,
            doctors: 0,
            clients: 0,
            rooms: 0,
            queue: 0,
            categories: 0,
            campaigns: 0,
            users: 0
        };
        
        this.saveLastSyncTimestamps(resetTimestamps);
        console.log('Zresetowano timestampy ostatniej synchronizacji');
    }
}

// Przykład użycia:
/*
const cacheClient = new CacheClient();

// Przykład sprawdzania zmian i pobierania danych
async function updateData(clientId) {
    try {
        const result = await cacheClient.checkAndFetchUpdates(clientId, {
            appointments: fetchAppointments,
            doctors: fetchDoctors,
            rooms: fetchRooms
        });
        
        if (result.hasChanges) {
            console.log('Pobrano zaktualizowane dane:', result.fetchedData);
            
            // Aktualizacja UI
            if (result.fetchedData.appointments) {
                updateAppointmentsUI(result.fetchedData.appointments);
            }
            
            if (result.fetchedData.doctors) {
                updateDoctorsUI(result.fetchedData.doctors);
            }
            
            if (result.fetchedData.rooms) {
                updateRoomsUI(result.fetchedData.rooms);
            }
        } else {
            console.log('Brak zmian w danych');
        }
    } catch (error) {
        console.error('Błąd podczas aktualizacji danych:', error);
    }
}

// Funkcje pobierania danych
async function fetchAppointments() {
    const response = await fetch('/api/v2/appointments/123');
    const data = await response.json();
    return data.data.appointments;
}

async function fetchDoctors() {
    const response = await fetch('/api/v2/doctors');
    const data = await response.json();
    return data.data.doctors;
}

async function fetchRooms() {
    const response = await fetch('/api/v2/rooms');
    const data = await response.json();
    return data.data.rooms;
}

// Wywołanie aktualizacji danych
updateData('123');

// Okresowe sprawdzanie zmian
setInterval(() => {
    updateData('123');
}, 30000); // co 30 sekund
*/

// Eksport klasy
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CacheClient;
} else {
    window.CacheClient = CacheClient;
}
