<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KtoOstatni.pl - Panel Lekarza</title>
    <meta name="description" content="Panel lekarza do zarządzania kolejką wizyt">
    <meta name="theme-color" content="#2c3e50">
    <link rel="manifest" href="/lekarz/manifest.json">
    <link rel="icon" type="image/png" sizes="192x192" href="/lekarz/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/lekarz/icons/icon-512x512.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/lekarz/styles.css">
</head>

<body>
    <div id="app">
        <!-- Ekran logowania -->
        <div id="loginScreen" class="screen active">
            <div class="login-card">
                <div class="login-icon">
                    <i class="fas fa-user-md fa-2x"></i>
                </div>
                <h3 class="login-title">KtoOstatni.pl</h3>
                <p class="login-subtitle">Wprowadź kod dostępu</p>

                <form id="loginForm">
                    <input type="text" id="accessCode" class="form-control" placeholder="xxxx-xxxx-xxxx" maxlength="14"
                        required>
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-sign-in-alt me-2"></i>Zaloguj się
                    </button>
                </form>

                <div id="loginError" class="alert alert-danger mt-3" style="display: none;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="errorMessage">Nieprawidłowy kod dostępu</span>
                </div>
            </div>
        </div>

        <!-- Panel główny -->
        <div id="mainScreen" class="screen">
            <div class="content-wrapper">
                <!-- Informacje o lekarzu -->
                <div class="doctor-info">
                    <div class="doctor-header">
                        <div id="doctorPhoto" class="doctor-photo">
                            <img id="doctorPhotoImg" src="" alt="Zdjęcie lekarza" style="display: none;">
                            <i id="doctorPhotoIcon" class="fas fa-user-md"></i>
                        </div>
                        <div class="doctor-details">
                            <div id="doctorName" class="doctor-name">Ładowanie...</div>
                        </div>
                    </div>
                    <div class="room-info">
                        <div class="room-controls">
                            <div class="room-line">
                                <div id="roomName" class="room-name">Ładowanie...</div>
                                <button id="changeRoomBtn" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-exchange-alt"></i>Zmień
                                </button>
                            </div>
                            <div class="date-controls">
                                <button id="prevDayBtn" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <input type="date" id="selectedDate" class="form-control form-control-sm date-input"
                                    value="">
                                <button id="nextDayBtn" class="btn btn-sm btn-outline-light">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>





                <!-- Aktualna wizyta -->
                <div class="current-appointment">
                    <div class="appointment-header">
                        <i class="fas fa-user-check me-2"></i>Aktualna wizyta
                    </div>
                    <div class="appointment-content">
                        <div id="currentAppointmentTime" class="appointment-time">--:--</div>
                        <div id="currentPatientPresence" class="patient-presence" style="display: none;">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <div id="currentPatientName" class="patient-name">Brak aktualnej wizyty</div>
                        <div class="appointment-actions">
                            <button id="previousBtn" class="btn btn-outline-secondary" disabled>
                                <i class="fas fa-arrow-left"></i>Poprz.
                            </button>
                            <button id="nextBtn" class="btn btn-success">
                                <i class="fas fa-arrow-right"></i>Następna
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Lista oczekujących -->
                <div class="waiting-list">
                    <div class="waiting-header">
                        <i class="fas fa-clock me-2"></i>Oczekujące wizyty
                    </div>
                    <div class="waiting-list-content" id="waitingList">
                        <div class="no-appointments">
                            <i class="fas fa-inbox"></i>
                            <p>Brak oczekujących wizyt</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statystyki -->
            <div class="stats">
                <div class="stat-item">
                    <div id="completedCount" class="stat-number completed">0</div>
                    <div class="stat-label">Zakończone</div>
                </div>
                <div class="stat-item">
                    <div id="waitingCount" class="stat-number waiting">0</div>
                    <div class="stat-label">Oczekujące</div>
                </div>
            </div>

            <!-- Przyciski nawigacji na dole -->
            <div class="navigation-buttons">
                <button id="installBtn" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    <span>Zainstaluj aplikację</span>
                </button>
                <button id="logoutBtn" class="btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Wyloguj</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Modal wyboru gabinetu -->
    <div id="roomModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-door-open me-2"></i>Wybierz gabinet
                </h5>
                <button id="closeRoomModalX" class="btn-close" type="button">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p class="text-muted mb-3">Wybierz gabinet, w którym chcesz przyjmować pacjentów:</p>
                <select id="roomSelector" class="form-select">
                    <option value="">Wybierz gabinet...</option>
                </select>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="closeRoomModal">
                    <i class="fas fa-times"></i>Anuluj
                </button>
                <button type="button" class="btn btn-primary" id="confirmRoomChange">
                    <i class="fas fa-check"></i>Zmień
                </button>
            </div>
        </div>
    </div>

    <!-- Loading spinner -->
    <div id="loadingSpinner" class="loading-overlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Ładowanie...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/lekarz/data-helper.js"></script>
    <script src="/lekarz/api-client.js"></script>
    <script src="/lekarz/app.js"></script>
</body>

</html>