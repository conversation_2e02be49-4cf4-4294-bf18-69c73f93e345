// Funkcje pomocnicze do normalizacji danych w PWA

class DataHelper {
    /**
     * Normalizuje imię i nazwisko - pierwsza litera wielka, reszta mała
     * Obsługuje polskie znaki diakryczne (ą, ć, ę, ł, ń, ó, ś, ź, ż)
     */
    static normalizeName(name) {
        if (!name || name.trim() === '') {
            return '';
        }

        // Usuń białe znaki z początku i końca
        const trimmedName = name.trim();

        // Podziel na słowa (spacje, myślniki, apostrofy)
        const words = trimmedName.split(/[\s\-]+/);

        const normalizedWords = words
            .filter(word => word.length > 0)
            .map(word => {
                // Konwertuj na małe litery
                const lowerWord = word.toLowerCase();

                // Pierwsza litera wielka z obsługą polskich znaków
                const firstChar = lowerWord.charAt(0);
                const restOfWord = lowerWord.slice(1);

                // Zamień pierwszą literę na wielką z obsługą polskich znaków
                const firstCharUpper = this.mbUcfirst(firstChar);

                return firstCharUpper + restOfWord;
            });

        return normalizedWords.join(' ');
    }

    /**
     * Zamienia pierwszą literę na wielką z obsługą polskich znaków diakrycznych
     */
    static mbUcfirst(string) {
        if (!string || string.length === 0) {
            return string;
        }

        // Mapa polskich znaków małych -> wielkich
        const polishChars = {
            'ą': 'Ą', 'ć': 'Ć', 'ę': 'Ę', 'ł': 'Ł',
            'ń': 'Ń', 'ó': 'Ó', 'ś': 'Ś', 'ź': 'Ź', 'ż': 'Ż'
        };

        const firstChar = string.charAt(0);

        // Sprawdź czy to polski znak diakryczny
        if (polishChars[firstChar]) {
            return polishChars[firstChar];
        }

        // Dla innych znaków użyj standardowej funkcji
        return firstChar.toUpperCase();
    }

    /**
     * Normalizuje pełne imię i nazwisko
     */
    static normalizeFullName(firstName, lastName) {
        const normalizedFirst = this.normalizeName(firstName);
        const normalizedLast = this.normalizeName(lastName);

        if (!normalizedFirst && !normalizedLast) {
            return '';
        }

        return `${normalizedFirst} ${normalizedLast}`.trim();
    }

    /**
     * Normalizuje specjalizację lekarza
     */
    static normalizeSpecialization(specialization) {
        if (!specialization || specialization.trim() === '') {
            return '';
        }

        return this.normalizeName(specialization);
    }

    /**
     * Normalizuje nazwę gabinetu/pokoju
     */
    static normalizeRoomName(roomName) {
        if (!roomName || roomName.trim() === '') {
            return '';
        }

        return this.normalizeName(roomName);
    }

    /**
     * Formatuje czas wizyty - wyciąga tylko godzinę z pełnej daty/czasu
     */
    static formatAppointmentTime(appointmentTime) {
        if (!appointmentTime || appointmentTime.trim() === '') {
            return '--:--';
        }

        // Jeśli czas zawiera datę (format: 2025-09-18 08:40), wyciągnij tylko czas
        if (appointmentTime.includes(' ')) {
            appointmentTime = appointmentTime.split(' ')[1];
        }

        // Jeśli czas ma sekundy (HH:MM:SS), usuń je
        if (appointmentTime.includes(':')) {
            const timeParts = appointmentTime.split(':');
            if (timeParts.length >= 2) {
                return `${timeParts[0]}:${timeParts[1]}`;
            }
        }

        // Jeśli czas jest już w formacie HH:MM, zwróć go
        if (/^\d{2}:\d{2}$/.test(appointmentTime)) {
            return appointmentTime;
        }

        // W przeciwnym razie spróbuj parsować przez Date
        try {
            const date = new Date(`1970-01-01 ${appointmentTime}`);
            if (!isNaN(date.getTime())) {
                return date.toTimeString().substring(0, 5);
            }
        } catch (e) {
            console.warn('Nie udało się sparsować czasu:', appointmentTime);
        }

        return appointmentTime; // Zwróć oryginalny czas jeśli nie udało się sparsować
    }
}
