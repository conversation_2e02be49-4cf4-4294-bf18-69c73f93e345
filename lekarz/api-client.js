/**
 * V2 API Client dla PWA
 * Obsługuje wszystkie operacje API z systemem monitorowania zmian
 */
class V2ApiClient {
    constructor(baseUrl = '/api/v2') {
        this.baseUrl = baseUrl;
        this.changeCheckInterval = null;
        this.changeCheckFrequency = 5000; // 5 sekund domyślnie
        this.isMonitoring = false;
        this.lastChangeCheck = null;
        this.changeCallbacks = [];
    }
    
    /**
     * Sprawdzenie zmian w systemie
     */
    async checkForChanges(clientId) {
        try {
            // Pobierz zapisane timestampy ostatniej synchronizacji
            const lastSyncTimestamps = this.getLastSyncTimestamps();
            
            // Użyj standardowego endpointu changes
            const endpoint = `${this.baseUrl}/changes/${clientId}`;
            
            const response = await fetch(endpoint, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.lastChangeCheck = new Date();
            
            // Zapisz nowe timestampy
            if (data.success && data.data && data.data.changes) {
                this.updateLastSyncTimestamps(data.data.changes);
            }
            
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd sprawdzania zmian:', error);
            return {
                success: false,
                hasChanges: false,
                error: error.message
            };
        }
    }
    
    /**
     * Pobiera zapisane timestampy ostatniej synchronizacji
     */
    getLastSyncTimestamps() {
        const stored = localStorage.getItem('lastSyncTimestamps');
        if (stored) {
            try {
                return JSON.parse(stored);
            } catch (error) {
                console.error('V2ApiClient: Błąd parsowania timestampów:', error);
            }
        }
        return {};
    }
    
    /**
     * Aktualizuje timestampy ostatniej synchronizacji
     */
    updateLastSyncTimestamps(changes) {
        const timestamps = this.getLastSyncTimestamps() || {};
        
        // Aktualizuj timestampy dla każdego typu danych
        Object.keys(changes).forEach(entityType => {
            if (changes[entityType] && changes[entityType].lastUpdate) {
                timestamps[entityType] = changes[entityType].lastUpdate;
            }
        });
        
        // Zapisz zaktualizowane timestampy
        localStorage.setItem('lastSyncTimestamps', JSON.stringify(timestamps));
    }
    
    /**
     * Pobieranie wizyt dla lekarza
     */
    async getAppointments(doctorId, date = null) {
        try {
            const dateParam = date ? `?date=${date}` : '';
            const response = await fetch(`${this.baseUrl}/appointments/${doctorId}${dateParam}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd pobierania wizyt:', error);
            return {
                success: false,
                appointments: [],
                error: error.message
            };
        }
    }
    
    /**
     * Aktualizacja wizyty
     */
    async updateAppointment(appointmentId, action) {
        try {
            const response = await fetch(`${this.baseUrl}/appointments/${appointmentId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ action: action })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd aktualizacji wizyty:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Wywołanie konkretnej wizyty
     */
    async callAppointment(appointmentId) {
        try {
            const response = await fetch(`${this.baseUrl}/appointments/${appointmentId}/call`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd wywołania wizyty:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Wywołanie konkretnej wizyty przez lekarza (nowy endpoint)
     */
    async callSpecificAppointment(roomId, doctorId, appointmentId) {
        try {
            const response = await fetch(`${this.baseUrl}/doctor/call-specific/${roomId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    doctor_id: doctorId,
                    appointment_id: appointmentId
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd wywołania konkretnej wizyty:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Pobieranie statusu kolejki
     */
    async getQueueStatus(clientId) {
        try {
            const response = await fetch(`${this.baseUrl}/queue/${clientId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('V2ApiClient: Błąd pobierania statusu kolejki:', error);
            return {
                success: false,
                appointments: [],
                error: error.message
            };
        }
    }
    
    /**
     * Uruchomienie monitorowania zmian
     */
    startChangeMonitoring(clientId, frequency = null) {
        if (this.isMonitoring) {
            console.log('V2ApiClient: Monitorowanie już aktywne');
            return;
        }
        
        this.changeCheckFrequency = frequency || this.changeCheckFrequency;
        this.isMonitoring = true;
        
        console.log(`V2ApiClient: Uruchamiam monitorowanie zmian co ${this.changeCheckFrequency}ms`);
        
        this.changeCheckInterval = setInterval(async () => {
            try {
                const changes = await this.checkForChanges(clientId);
                
                if (changes.success && changes.hasChanges) {
                    console.log('V2ApiClient: Wykryto zmiany w systemie:', changes.changes);
                    
                    // Wywołaj wszystkie zarejestrowane callbacki
                    this.changeCallbacks.forEach(callback => {
                        try {
                            callback(changes);
                        } catch (error) {
                            console.error('V2ApiClient: Błąd w callback:', error);
                        }
                    });
                }
            } catch (error) {
                console.error('V2ApiClient: Błąd monitorowania zmian:', error);
            }
        }, this.changeCheckFrequency);
    }
    
    /**
     * Zatrzymanie monitorowania zmian
     */
    stopChangeMonitoring() {
        if (this.changeCheckInterval) {
            clearInterval(this.changeCheckInterval);
            this.changeCheckInterval = null;
        }
        this.isMonitoring = false;
        console.log('V2ApiClient: Zatrzymano monitorowanie zmian');
    }
    
    /**
     * Rejestracja callbacka dla zmian
     */
    onChanges(callback) {
        if (typeof callback === 'function') {
            this.changeCallbacks.push(callback);
            console.log('V2ApiClient: Zarejestrowano callback dla zmian');
        }
    }
    
    /**
     * Usunięcie callbacka
     */
    offChanges(callback) {
        const index = this.changeCallbacks.indexOf(callback);
        if (index > -1) {
            this.changeCallbacks.splice(index, 1);
            console.log('V2ApiClient: Usunięto callback dla zmian');
        }
    }
    
    /**
     * Ustawienie częstotliwości sprawdzania zmian
     */
    setChangeCheckFrequency(frequency) {
        this.changeCheckFrequency = frequency;
        console.log(`V2ApiClient: Ustawiono częstotliwość sprawdzania zmian na ${frequency}ms`);
        
        // Restart monitorowania jeśli jest aktywne
        if (this.isMonitoring) {
            this.stopChangeMonitoring();
            // Restart będzie wymagał ponownego wywołania startChangeMonitoring z clientId
        }
    }
    
    /**
     * Pobranie statystyk monitorowania
     */
    getMonitoringStats() {
        return {
            isMonitoring: this.isMonitoring,
            frequency: this.changeCheckFrequency,
            lastCheck: this.lastChangeCheck,
            callbacksCount: this.changeCallbacks.length
        };
    }
    
    /**
     * Test połączenia z API
     */
    async testConnection() {
        try {
            const response = await fetch(`${this.baseUrl}/changes/test`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            return {
                success: response.ok,
                status: response.status,
                message: response.ok ? 'Połączenie OK' : `Błąd HTTP: ${response.status}`
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Eksport dla Node.js (jeśli potrzebny)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = V2ApiClient;
}
