<?php

/**
 * Migracja: Aktualizacja systemu video
 * 1. Przeniesienie kategorii z video_categories do ads_categories
 * 2. Dodanie tabeli ads_advertiser
 * 3. Przeniesienie ustawień auto-akceptacji do settings
 * 4. Dodanie kolumn dla edycji reklam (local_file_path, display_duration)
 */

define('KTOOSTATNI_APP', true);
require_once __DIR__ . '/config.php';

echo "=== Migracja: Aktualizacja systemu video ===\n\n";

try {
    $db = Database::getInstance();

    // Wyłącz foreign keys dla tej migracji
    $db->execute("PRAGMA foreign_keys=OFF");

    // 1. Sprawdź czy tabela ads_advertiser istnieje
    $tableExists = $db->fetchOne("
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='ads_advertiser'
    ");

    if (!$tableExists) {
        // Utwórz tabelę ads_advertiser
        $sql = "
            CREATE TABLE ads_advertiser (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ";

        $db->execute($sql);
        echo "✅ Utworzono tabelę ads_advertiser.\n";

        // Dodaj przykładowych reklamodawców
        $advertisers = [
            'Apteka Centralna',
            'Klinika Zdrowie',
            'Laboratorium Medyczne',
            'Centrum Rehabilitacji',
            'Przychodnia Rodzinna'
        ];

        foreach ($advertisers as $name) {
            $db->execute(
                "INSERT INTO ads_advertiser (name) VALUES (?)",
                [$name]
            );
        }

        echo "✅ Dodano przykładowych reklamodawców.\n";
    } else {
        echo "✅ Tabela ads_advertiser już istnieje.\n";
    }

    // 2. Sprawdź czy kolumny local_file_path i display_duration istnieją w tabeli ads
    $columns = $db->fetchAll("PRAGMA table_info(ads)");
    $hasLocalFile = false;
    $hasDisplayDuration = false;
    $hasAdvertiserIdRef = false;

    foreach ($columns as $column) {
        if ($column['name'] === 'local_file_path') {
            $hasLocalFile = true;
        }
        if ($column['name'] === 'display_duration') {
            $hasDisplayDuration = true;
        }
        if ($column['name'] === 'ads_advertiser_id') {
            $hasAdvertiserIdRef = true;
        }
    }

    if (!$hasLocalFile) {
        $db->execute("ALTER TABLE ads ADD COLUMN local_file_path VARCHAR(255)");
        echo "✅ Dodano kolumnę local_file_path do tabeli ads.\n";
    }

    if (!$hasDisplayDuration) {
        $db->execute("ALTER TABLE ads ADD COLUMN display_duration INTEGER DEFAULT 30");
        echo "✅ Dodano kolumnę display_duration do tabeli ads.\n";
    }

    if (!$hasAdvertiserIdRef) {
        $db->execute("ALTER TABLE ads ADD COLUMN ads_advertiser_id INTEGER");
        echo "✅ Dodano kolumnę ads_advertiser_id do tabeli ads.\n";
    }

    // 3. Migracja kategorii z video_categories do ads_categories
    $videoCategories = $db->fetchAll("SELECT * FROM video_categories");

    if (!empty($videoCategories)) {
        echo "📋 Migracja kategorii z video_categories do ads_categories...\n";

        foreach ($videoCategories as $category) {
            // Sprawdź czy kategoria już istnieje w ads_categories
            $existing = $db->fetchOne(
                "SELECT id FROM ads_categories WHERE name = ?",
                [$category['name']]
            );

            if (!$existing) {
                // Dodaj kategorię do ads_categories
                $db->execute(
                    "INSERT INTO ads_categories (name, description, created_at) VALUES (?, ?, ?)",
                    [$category['name'], $category['description'], $category['created_at']]
                );

                $newCategoryId = $db->lastInsertId();
                echo "  ✅ Przeniesiono kategorię: {$category['name']}\n";
            } else {
                $newCategoryId = $existing['id'];
                echo "  ⚠️  Kategoria już istnieje: {$category['name']}\n";
            }

            // Zaktualizuj ads aby używały category_id zamiast video_category_id
            // Sprawdź czy kolumna category_id istnieje
            $columns = $db->fetchAll("PRAGMA table_info(ads)");
            $hasCategoryId = false;
            foreach ($columns as $col) {
                if ($col['name'] === 'category_id') {
                    $hasCategoryId = true;
                    break;
                }
            }

            if ($hasCategoryId) {
                $db->execute(
                    "UPDATE ads SET category_id = ? WHERE video_category_id = ?",
                    [$newCategoryId, $category['id']]
                );
            } else {
                echo "  ⚠️  Kolumna category_id nie istnieje w tabeli ads\n";
            }

            // Zapisz ustawienie auto-akceptacji w settings
            if ($category['auto_accept']) {
                $settingKey = "video_category_auto_accept_{$newCategoryId}";
                $db->execute(
                    "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime('now'))",
                    [$settingKey, '1']
                );
                echo "  ✅ Zapisano ustawienie auto-akceptacji dla kategorii: {$category['name']}\n";
            }
        }
    }

    // 4. Usuń kolumnę video_category_id z ads (nie można w SQLite, więc pozostawiamy)
    echo "⚠️  Kolumna video_category_id pozostaje w tabeli ads (ograniczenie SQLite).\n";

    echo "\n=== Migracja zakończona pomyślnie! ===\n";
    echo "\nPodsumowanie zmian:\n";
    echo "- ✅ Utworzono tabelę ads_advertiser\n";
    echo "- ✅ Dodano kolumny local_file_path, display_duration, ads_advertiser_id do ads\n";
    echo "- ✅ Przeniesiono kategorie do ads_categories\n";
    echo "- ✅ Zapisano ustawienia auto-akceptacji w settings\n";
    echo "- ✅ Zaktualizowano referencje kategorii w ads\n";

    // Włącz z powrotem foreign keys
    $db->execute("PRAGMA foreign_keys=ON");
} catch (Exception $e) {
    echo "❌ Błąd podczas migracji: " . $e->getMessage() . "\n";
    exit(1);
}
