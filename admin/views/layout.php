<!DOCTYPE html>
<html lang="pl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Panel Administracyjny' ?> - <?= APP_NAME ?></title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin />
    <link rel="stylesheet" as="style" onload="this.rel='stylesheet'"
        href="https://fonts.googleapis.com/css2?display=swap&family=Inter:wght@400;500;600;700;900&family=Noto+Sans:wght@400;500;700;900" />

    <!-- Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', 'Noto Sans', sans-serif;
        }

        /* Status colors */
        .status-working {
            @apply bg-green-100 text-green-800 border-green-200;
        }

        .status-ready {
            @apply bg-blue-100 text-blue-800 border-blue-200;
        }

        .status-finished {
            @apply bg-gray-100 text-gray-800 border-gray-200;
        }

        .status-no-appointments {
            @apply bg-slate-100 text-slate-800 border-slate-200;
        }

        /* Patient status colors */
        .patient-waiting {
            @apply bg-blue-50 border-blue-200;
        }

        .patient-current {
            @apply bg-green-50 border-green-200;
        }

        .patient-completed {
            @apply bg-gray-50 border-gray-200;
        }

        .patient-delayed {
            @apply bg-red-50 border-red-200;
        }

        /* Custom scrollbar */
        .custom-scroll::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scroll::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scroll::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Line clamp utility */
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Compact queue view */
        .queue-compact {
            max-height: 400px;
        }

        /* Animation for current patient */
        @keyframes pulse-green {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        .animate-pulse-green {
            animation: pulse-green 2s infinite;
        }
    </style>
</head>

<body class="bg-slate-50 text-slate-900">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white border-b border-slate-200 shadow-sm sticky top-0 z-20">
            <div class="w-full px-6">
                <div class="flex items-center justify-between h-14">
                    <!-- Logo and Title -->
                    <div class="flex items-center gap-4">
                        <div class="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                            <span class="material-icons-outlined text-white text-xl">medical_services</span>
                        </div>
                        <div>
                            <h1 class="text-lg font-bold text-slate-900"><?= APP_NAME ?></h1>
                            <p class="text-xs text-slate-600">System zarządzania kolejkami</p>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="hidden md:flex items-center space-x-4">
                        <a href="/admin/pulpit" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">dashboard</span>
                            Pulpit
                        </a>
                        <a href="/admin/kolejki" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">queue</span>
                            Kolejki
                        </a>
                        <a href="/admin/lekarze" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">people</span>
                            Lekarze
                        </a>
                        <a href="/admin/wyswietlacze" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">tv</span>
                            Wyświetlacze
                        </a>
                        <a href="/admin/video" class="px-3 py-2 text-sm font-medium text-slate-700 hover:text-indigo-600 hover:bg-slate-100 rounded-lg transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">video_library</span>
                            Video
                        </a>

                    </nav>

                    <!-- User menu and controls -->
                    <div class="flex items-center gap-4">
                        <!-- Current Time -->
                        <div class="text-right hidden sm:block">
                            <div class="text-sm font-semibold text-slate-900" id="current-time"><?= date('H:i:s') ?></div>
                            <div class="text-xs text-slate-600"><?= formatDate(date('Y-m-d')) ?></div>
                        </div>

                        <!-- User info -->
                        <div class="flex items-center gap-2">
                            <?php if (isset($user) && $user): ?>
                                <div class="text-right hidden sm:block">
                                    <div class="text-sm font-medium text-slate-900"><?= htmlspecialchars($user['username'] ?? '') ?></div> <!-- UWAGA: Kolumna company_name została usunięta -->
                                    <div class="text-xs text-slate-600">&nbsp;</div>
                                </div>
                            <?php endif; ?>

                            <!-- Settings -->
                            <a href="/admin/settings" class="p-2 rounded-lg hover:bg-slate-100 transition-colors" title="Ustawienia">
                                <span class="material-icons-outlined text-slate-600">settings</span>
                            </a>

                            <!-- Logout -->
                            <a href="/admin/logout" class="p-2 rounded-lg hover:bg-slate-100 transition-colors" title="Wyloguj">
                                <span class="material-icons-outlined text-slate-600">logout</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 w-full">
            <?= $content ?>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-slate-200 py-4">
            <div class="w-full px-6">
                <div class="flex items-center justify-between text-sm text-slate-600">
                    <div>
                        <?= APP_NAME ?> v<?= APP_VERSION ?> &copy; <?= date('Y') ?>
                    </div>
                    <div class="flex items-center gap-4">
                        <span>Ostatnia aktualizacja: <span id="last-update"><?= date('H:i:s') ?></span></span>
                        <button onclick="location.reload()" class="text-indigo-600 hover:text-indigo-800 transition-colors">
                            <span class="material-icons-outlined text-sm">refresh</span>
                        </button>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('pl-PL');
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }

            const updateElement = document.getElementById('last-update');
            if (updateElement) {
                updateElement.textContent = timeString;
            }
        }

        setInterval(updateTime, 1000);
        updateTime();

        // Auto refresh disabled per user request
        // setInterval(function() {
        //     if (document.visibilityState === 'visible') {
        //         location.reload();
        //     }
        // }, <?= QUEUE_REFRESH_INTERVAL * 1000 ?>);

        // Scroll to current appointment on page load
        function scrollToCurrentAppointment() {
            const currentAppointment = document.querySelector('.patient-current');
            if (currentAppointment) {
                currentAppointment.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
                // Highlight current appointment briefly
                currentAppointment.style.boxShadow = '0 0 10px rgba(34, 197, 94, 0.5)';
                setTimeout(() => {
                    currentAppointment.style.boxShadow = '';
                }, 2000);
            }
        }

        // Run on page load
        document.addEventListener('DOMContentLoaded', scrollToCurrentAppointment);

        // Also run after a short delay to ensure all content is loaded
        setTimeout(scrollToCurrentAppointment, 500);

        // AJAX helper functions
        function callPatient(appointmentId) {
            fetch('/admin/queue/call-patient', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'appointment_id=' + appointmentId
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Błąd: ' + (data.message || 'Nie udało się wezwać pacjenta'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Wystąpił błąd podczas wzywania pacjenta');
                });
        }

        function completeAppointment(appointmentId) {
            if (confirm('Czy na pewno chcesz zakończyć tę wizytę?')) {
                fetch('/admin/queue/complete-appointment', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'appointment_id=' + appointmentId
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Błąd: ' + (data.message || 'Nie udało się zakończyć wizyty'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Wystąpił błąd podczas kończenia wizyty');
                    });
            }
        }

        // Date navigation
        function changeDate(direction) {
            const currentDate = new Date('<?= $selectedDate ?? date('Y-m-d') ?>');
            currentDate.setDate(currentDate.getDate() + direction);
            const newDate = currentDate.toISOString().split('T')[0];
            window.location.href = '?date=' + newDate;
        }

        function selectDate() {
            const dateInput = document.getElementById('date-picker');
            if (dateInput.value) {
                window.location.href = '?date=' + dateInput.value;
            }
        }

        // Filter functions
        function toggleFilter() {
            const withAppointments = document.getElementById('filter-with-appointments').checked;
            const withoutAppointments = document.getElementById('filter-without-appointments').checked;
            const working = document.getElementById('filter-working').checked;

            const doctorCards = document.querySelectorAll('.doctor-card');

            doctorCards.forEach(card => {
                const hasAppointments = card.dataset.hasAppointments === 'true';
                const status = card.dataset.status;

                let show = false;

                if (hasAppointments && withAppointments) {
                    show = true;
                } else if (!hasAppointments && withoutAppointments) {
                    show = true;
                }

                if (working && status === 'working') {
                    show = true;
                }

                // If no filters are checked, show all
                if (!withAppointments && !withoutAppointments && !working) {
                    show = true;
                }

                card.style.display = show ? 'block' : 'none';
            });
        }
    </script>
</body>

</html>