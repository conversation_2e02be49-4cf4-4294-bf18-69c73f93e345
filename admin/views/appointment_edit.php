<!-- <PERSON><PERSON><PERSON><PERSON> wizyty -->
<div class="px-6 py-4">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-slate-900">Edytu<PERSON> wizytę</h1>
            <p class="text-slate-600 mt-1">Zmień szczegóły wizyty</p>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($error)): ?>
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-red-600 mr-2">error</span>
                    <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                    <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                </div>
            </div>
        <?php endif; ?>

        <!-- Formularz -->
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <form method="POST" class="space-y-6">
                <!-- Informacje o lekarzu -->
                <div class="bg-slate-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-2">Lekarz</h3>
                    <?php
                    $doctor = null;
                    foreach ($doctors as $d) {
                        if ($d['id'] == $appointment['doctor_id']) {
                            $doctor = $d;
                            break;
                        }
                    }
                    ?>
                    <p class="text-slate-700">
                        <?= htmlspecialchars($doctor ? $doctor['first_name'] . ' ' . $doctor['last_name'] : 'Nieznany lekarz') ?>
                    </p>
                    <?php if ($doctor && $doctor['specialization']): ?>
                        <p class="text-sm text-slate-600"><?= htmlspecialchars($doctor['specialization']) ?></p>
                    <?php endif; ?>
                </div>

                <!-- Dane pacjenta -->
                <div>
                    <label for="patient_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Imię i nazwisko pacjenta *
                    </label>
                    <input type="text"
                        id="patient_name"
                        name="patient_name"
                        value="<?= htmlspecialchars($appointment['patient_name']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Data wizyty -->
                <div>
                    <label for="appointment_date" class="block text-sm font-medium text-slate-700 mb-2">
                        Data wizyty *
                    </label>
                    <input type="date"
                        id="appointment_date"
                        name="appointment_date"
                        value="<?= htmlspecialchars($appointment['appointment_date']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Godzina wizyty -->
                <div>
                    <label for="appointment_time" class="block text-sm font-medium text-slate-700 mb-2">
                        Godzina wizyty *
                    </label>
                    <input type="time"
                        id="appointment_time"
                        name="appointment_time"
                        value="<?= formatTime($appointment['appointment_time']) ?>"
                        class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        required>
                </div>

                <!-- Status wizyty -->
                <div class="bg-slate-50 rounded-lg p-4">
                    <h3 class="font-medium text-slate-900 mb-2">Status wizyty</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-slate-600">Aktualny status:</span>
                            <span class="ml-2 font-medium">
                                <?php
                                $statusLabels = [
                                    'waiting' => 'Oczekuje',
                                    'current' => 'Aktualna',
                                    'completed' => 'Zakończona',
                                    'closed' => 'Zakończona'
                                ];
                                echo $statusLabels[$appointment['status']] ?? 'Nieznany';
                                ?>
                            </span>
                        </div>
                        <div>
                            <span class="text-slate-600">Utworzona:</span>
                            <span class="ml-2"><?= formatDateTime($appointment['created_at']) ?></span>
                        </div>
                        <?php if ($appointment['called_at']): ?>
                            <div>
                                <span class="text-slate-600">Wywołana:</span>
                                <span class="ml-2"><?= formatDateTime($appointment['called_at']) ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if ($appointment['completed_at']): ?>
                            <div>
                                <span class="text-slate-600">Zakończona:</span>
                                <span class="ml-2"><?= formatDateTime($appointment['completed_at']) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-between pt-6 border-t border-slate-200">
                    <a href="/admin/pulpit?date=<?= htmlspecialchars($appointment['appointment_date']) ?>"
                        class="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors">
                        ← Powrót do pulpitu
                    </a>

                    <div class="flex gap-3">
                        <button type="button"
                            onclick="deleteAppointment(<?= $appointment['id'] ?>)"
                            class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">delete</span>
                            Usuń wizytę
                        </button>

                        <button type="submit"
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                            <span class="material-icons-outlined text-sm mr-1">save</span>
                            Zapisz zmiany
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function deleteAppointment(appointmentId) {
        if (confirm('Czy na pewno chcesz usunąć tę wizytę? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/appointments/delete/' + appointmentId;
        }
    }
</script>