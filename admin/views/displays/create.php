<!-- Dodawan<PERSON> wyświetlacza -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900"><PERSON>da<PERSON> wyświetlacz</h1>
            <p class="text-slate-600 mt-1">Utwórz nowy wyświetlacz w systemie</p>
        </div>

        <a href="/admin/wyswietlacze"
            class="flex items-center gap-2 px-4 py-2 bg-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors">
            <span class="material-icons-outlined text-sm">arrow_back</span>
            Powrót
        </a>
    </div>

    <!-- Formularz -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        <form method="POST" action="/admin/wyswietlacze/store">
            <div class="space-y-6">
                <!-- Nazwa wyświetlacza -->
                <div>
                    <label for="display_name" class="block text-sm font-medium text-slate-700 mb-2">
                        Nazwa wyświetlacza *
                    </label>
                    <input type="text" 
                           id="display_name" 
                           name="display_name" 
                           class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="np. Recepcja, Poczekalnia A, Sala główna"
                           required>
                    <p class="text-sm text-slate-600 mt-1">
                        Podaj opisową nazwę, która pomoże zidentyfikować lokalizację wyświetlacza
                    </p>
                </div>

                <!-- Informacje -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start">
                        <span class="material-icons-outlined text-blue-600 mr-3 mt-0.5">info</span>
                        <div>
                            <h3 class="text-sm font-medium text-blue-900 mb-2">Jak to działa?</h3>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• System automatycznie wygeneruje unikalny 6-znakowy kod wyświetlacza</li>
                                <li>• Wyświetlacz będzie dostępny pod adresem: <code>/display/[kod]</code></li>
                                <li>• Po utworzeniu wyświetlacz będzie wymagał sparowania w zakładce "Wyświetlacze"</li>
                                <li>• Na ekranie wyświetlacza pojawi się kod parowania do wpisania w panelu</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Przyciski -->
                <div class="flex items-center justify-end gap-4 pt-6 border-t border-slate-200">
                    <a href="/admin/wyswietlacze" 
                       class="px-4 py-2 text-slate-700 bg-white border border-slate-300 rounded-lg hover:bg-slate-50 transition-colors">
                        Anuluj
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-1">add</span>
                        Dodaj wyświetlacz
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
