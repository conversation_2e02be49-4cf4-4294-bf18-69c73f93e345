<!-- Zarząd<PERSON>ie wyświetlaczami -->
<div class="px-6 py-4">
    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <span class="material-icons-outlined text-green-400 text-sm mr-2">check_circle</span>
                <span class="text-sm text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex">
                <span class="material-icons-outlined text-red-400 text-sm mr-2">error</span>
                <span class="text-sm text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Zarządzanie wyświetlaczami</h1>
            <p class="text-slate-600 mt-1">Paruj wyświetlacze i zarządzaj ich statusem</p>
        </div>

        <a href="/admin/wyswietlacze/create"
            class="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            <span class="material-icons-outlined text-sm">add</span>
            Dodaj wyświetlacz
        </a>
    </div>

    <!-- Formularz parowania -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 mb-6">
        <h2 class="text-lg font-semibold text-slate-900 mb-4">
            <span class="material-icons-outlined text-indigo-600 mr-2" style="vertical-align: middle;">link</span>
            Sparuj wyświetlacz
        </h2>

        <form method="POST" action="/admin/wyswietlacze/pair" class="flex gap-4 items-end">
            <div class="flex-1">
                <label for="pairing_code" class="block text-sm font-medium text-slate-700 mb-2">
                    Kod parowania
                </label>
                <input type="text"
                    id="pairing_code"
                    name="pairing_code"
                    class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 uppercase"
                    placeholder="np. ABC123"
                    maxlength="6"
                    style="text-transform: uppercase;"
                    required>
            </div>
            <button type="submit"
                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <span class="material-icons-outlined text-sm mr-1">link</span>
                Sparuj
            </button>
        </form>

        <p class="text-sm text-slate-600 mt-3">
            <span class="material-icons-outlined text-xs mr-1" style="vertical-align: middle;">info</span>
            Wpisz 6-znakowy kod wyświetlany na ekranie wyświetlacza
        </p>
    </div>

    <!-- Lista wyświetlaczy -->
    <div class="bg-white rounded-lg shadow-sm border border-slate-200">
        <div class="px-6 py-4 border-b border-slate-200">
            <h2 class="text-lg font-semibold text-slate-900">Lista wyświetlaczy</h2>
        </div>

        <?php if (empty($displays)): ?>
            <div class="p-8 text-center">
                <span class="material-icons-outlined text-slate-400 text-4xl mb-4">tv_off</span>
                <p class="text-slate-600">Brak wyświetlaczy w systemie</p>
                <a href="/admin/wyswietlacze/create"
                    class="inline-flex items-center gap-2 mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                    <span class="material-icons-outlined text-sm">add</span>
                    Dodaj pierwszy wyświetlacz
                </a>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-slate-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Wyświetlacz
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Kod
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Status parowania
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Status połączenia
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Ostatnia aktywność
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                                Akcje
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-slate-200">
                        <?php foreach ($displays as $display): ?>
                            <tr class="hover:bg-slate-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <span class="material-icons-outlined text-slate-400 mr-3">tv</span>
                                        <div>
                                            <div class="text-sm font-medium text-slate-900">
                                                <?= htmlspecialchars($display['display_name']) ?>
                                            </div>
                                            <div class="text-sm text-slate-500">
                                                ID: <?= $display['id'] ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <code class="px-2 py-1 bg-slate-100 rounded text-sm font-mono">
                                        <?= strtoupper($display['display_code']) ?>
                                    </code>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($display['pairing_status'] === 'paired'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <span class="material-icons-outlined text-xs mr-1">check_circle</span>
                                            Sparowany
                                        </span>
                                    <?php elseif ($display['pairing_status'] === 'pending'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <span class="material-icons-outlined text-xs mr-1">schedule</span>
                                            Oczekuje
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                                            <span class="material-icons-outlined text-xs mr-1">link_off</span>
                                            Nie sparowany
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($display['is_online']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <span class="w-2 h-2 bg-green-400 rounded-full mr-1"></span>
                                            Online
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
                                            <span class="w-2 h-2 bg-slate-400 rounded-full mr-1"></span>
                                            Offline
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-500">
                                    <?php if ($display['last_heartbeat']): ?>
                                        <?= formatDateTime($display['last_heartbeat']) ?>
                                    <?php else: ?>
                                        Nigdy
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center gap-2">
                                        <!-- Przycisk edycji nazwy -->
                                        <button type="button"
                                            class="text-blue-600 hover:text-blue-900"
                                            onclick="editDisplayName(<?= $display['id'] ?>, '<?= htmlspecialchars($display['display_name'], ENT_QUOTES) ?>')">
                                            <span class="material-icons-outlined text-sm">edit</span>
                                        </button>

                                        <?php if ($display['pairing_status'] === 'paired'): ?>
                                            <form method="POST" action="/admin/wyswietlacze/unpair" class="inline">
                                                <input type="hidden" name="display_id" value="<?= $display['id'] ?>">
                                                <button type="submit"
                                                    class="text-red-600 hover:text-red-900"
                                                    onclick="return confirm('Czy na pewno chcesz usunąć parowanie tego wyświetlacza?')">
                                                    <span class="material-icons-outlined text-sm">link_off</span>
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <form method="POST" action="/admin/wyswietlacze/delete" class="inline">
                                            <input type="hidden" name="display_id" value="<?= $display['id'] ?>">
                                            <button type="submit"
                                                class="text-red-600 hover:text-red-900"
                                                onclick="return confirm('Czy na pewno chcesz usunąć ten wyświetlacz?')">
                                                <span class="material-icons-outlined text-sm">delete</span>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Modal edycji nazwy wyświetlacza -->
<div id="editDisplayModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Edytuj nazwę wyświetlacza</h3>
            <form id="editDisplayForm" method="POST" action="/admin/wyswietlacze/update-name">
                <input type="hidden" id="editDisplayId" name="display_id" value="">
                <div class="mb-4">
                    <label for="editDisplayName" class="block text-sm font-medium text-gray-700 mb-2">
                        Nazwa wyświetlacza
                    </label>
                    <input type="text"
                        id="editDisplayName"
                        name="display_name"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                        maxlength="100">
                </div>
                <div class="flex justify-end gap-3">
                    <button type="button"
                        onclick="closeEditModal()"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Anuluj
                    </button>
                    <button type="submit"
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Zapisz
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function editDisplayName(displayId, currentName) {
        document.getElementById('editDisplayId').value = displayId;
        document.getElementById('editDisplayName').value = currentName;
        document.getElementById('editDisplayModal').classList.remove('hidden');
    }

    function closeEditModal() {
        document.getElementById('editDisplayModal').classList.add('hidden');
    }

    // Zamknij modal po kliknięciu poza nim
    document.getElementById('editDisplayModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeEditModal();
        }
    });
</script>

<script>
    // Auto-uppercase dla kodu parowania
    document.getElementById('pairing_code').addEventListener('input', function(e) {
        e.target.value = e.target.value.toUpperCase();
    });
</script>