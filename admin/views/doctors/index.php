<!-- Zarządzanie lekarzami -->
<div class="px-6 py-4">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-bold text-slate-900">Zarządzanie lekarzami</h1>
            <p class="text-slate-600 mt-1">Dodawaj, edytuj i zarządzaj profilami lekarzy</p>
        </div>

        <a href="/admin/lekarze/create"
            class="flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            <span class="material-icons-outlined text-sm">add</span>
            Dodaj lekarza
        </a>
    </div>

    <!-- Pole wyszukiwania -->
    <?php if (!empty($doctors)): ?>
        <div class="mb-6">
            <div class="relative">
                <span class="material-icons-outlined absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400">search</span>
                <input type="text" id="searchInput" placeholder="Wyszukaj lekarza po imieniu, nazwisku lub specjalizacji..."
                    class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors">
            </div>
        </div>
    <?php endif; ?>

    <!-- Komunikaty -->
    <?php if (isset($_SESSION['success'])): ?>
        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-green-600 mr-2">check_circle</span>
                <span class="text-green-800"><?= htmlspecialchars($_SESSION['success']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <span class="material-icons-outlined text-red-600 mr-2">error</span>
                <span class="text-red-800"><?= htmlspecialchars($_SESSION['error']) ?></span>
            </div>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Lista lekarzy -->
    <?php if (empty($doctors)): ?>
        <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
            <span class="material-icons-outlined text-slate-400 text-6xl mb-4">person_add</span>
            <h3 class="text-lg font-medium text-slate-900 mb-2">Brak lekarzy</h3>
            <p class="text-slate-600 mb-4">Dodaj pierwszego lekarza, aby rozpocząć zarządzanie kolejkami.</p>
            <a href="/admin/lekarze/create"
                class="inline-flex items-center gap-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                <span class="material-icons-outlined text-sm">add</span>
                Dodaj lekarza
            </a>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="doctorsGrid">
            <?php foreach ($doctors as $doctor): ?>
                <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 doctor-card"
                    data-name="<?= htmlspecialchars(strtolower($doctor['first_name'] . ' ' . $doctor['last_name'])) ?>"
                    data-specialization="<?= htmlspecialchars(strtolower($doctor['specialization'] ?? '')) ?>">
                    <!-- Zdjęcie i podstawowe info -->
                    <div class="flex items-center mb-4">
                        <img src="<?= getDoctorPhotoUrl($doctor['photo_url']) ?>"
                            alt="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>"
                            class="w-12 h-12 rounded-full object-cover mr-3">
                        <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-slate-900 truncate" title="<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>">
                                <?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>
                            </h3>
                            <?php if ($doctor['specialization']): ?>
                                <p class="text-sm text-slate-600 truncate" title="<?= htmlspecialchars($doctor['specialization']) ?>">
                                    <?= htmlspecialchars($doctor['specialization']) ?>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Informacje dodatkowe -->
                    <div class="space-y-2 mb-4">
                        <?php if ($doctor['access_code']): ?>
                            <div class="flex items-center text-sm text-slate-600 bg-indigo-50 p-2 rounded-lg">
                                <span class="material-icons-outlined text-sm mr-2 text-indigo-600">key</span>
                                <div class="flex-1">
                                    <div class="text-xs text-slate-500 mb-1">Kod dostępu PWA:</div>
                                    <div class="font-mono font-semibold text-indigo-700 tracking-wider">
                                        <?= htmlspecialchars($doctor['access_code']) ?>
                                    </div>
                                </div>
                                <div class="flex gap-1">
                                    <button onclick="copyAccessCode('<?= htmlspecialchars($doctor['access_code']) ?>')"
                                        class="p-1 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-100 rounded transition-colors"
                                        title="Kopiuj kod">
                                        <span class="material-icons-outlined text-sm">content_copy</span>
                                    </button>
                                    <button onclick="regenerateAccessCode(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>')"
                                        class="p-1 text-amber-600 hover:text-amber-800 hover:bg-amber-100 rounded transition-colors"
                                        title="Regeneruj kod">
                                        <span class="material-icons-outlined text-sm">refresh</span>
                                    </button>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center text-sm text-amber-600 bg-amber-50 p-2 rounded-lg">
                                <span class="material-icons-outlined text-sm mr-2">warning</span>
                                <span>Brak kodu dostępu PWA</span>
                            </div>
                        <?php endif; ?>

                        <?php if ($doctor['default_room_name']): ?>
                            <div class="flex items-center text-sm text-slate-600">
                                <span class="material-icons-outlined text-sm mr-2">room</span>
                                <?= htmlspecialchars($doctor['default_room_name']) ?>
                            </div>
                        <?php endif; ?>

                        <div class="flex items-center text-sm text-slate-600">
                            <span class="material-icons-outlined text-sm mr-2">schedule</span>
                            Utworzony: <?= formatDate($doctor['created_at']) ?>
                        </div>

                        <?php if (isset($doctor['updated_at']) && $doctor['updated_at']): ?>
                            <div class="flex items-center text-sm text-slate-600">
                                <span class="material-icons-outlined text-sm mr-2">update</span>
                                Zaktualizowany: <?= formatDate($doctor['updated_at']) ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <?php if ($doctor['active']): ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <span class="w-1.5 h-1.5 bg-green-600 rounded-full mr-1"></span>
                                Aktywny
                            </span>
                        <?php else: ?>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <span class="w-1.5 h-1.5 bg-gray-600 rounded-full mr-1"></span>
                                Nieaktywny
                            </span>
                        <?php endif; ?>
                    </div>

                    <!-- Akcje -->
                    <div class="flex items-center justify-between pt-4 border-t border-slate-200">
                        <a href="/admin/kolejki?doctor_id=<?= $doctor['id'] ?>"
                            class="text-sm text-indigo-600 hover:text-indigo-800 transition-colors">
                            Zobacz kolejkę
                        </a>

                        <div class="flex gap-2">
                            <a href="/admin/lekarze/edit/<?= $doctor['id'] ?>"
                                class="p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors"
                                title="Edytuj">
                                <span class="material-icons-outlined text-sm">edit</span>
                            </a>

                            <button onclick="deleteDoctor(<?= $doctor['id'] ?>, '<?= htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']) ?>')"
                                class="p-2 text-red-600 hover:text-red-800 hover:bg-red-100 rounded-lg transition-colors"
                                title="Usuń">
                                <span class="material-icons-outlined text-sm">delete</span>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<script>
    function deleteDoctor(doctorId, doctorName) {
        if (confirm('Czy na pewno chcesz usunąć lekarza "' + doctorName + '"? Ta operacja jest nieodwracalna.')) {
            window.location.href = '/admin/lekarze/delete/' + doctorId;
        }
    }

    function copyAccessCode(code) {
        navigator.clipboard.writeText(code).then(function() {
            // Pokaż komunikat o skopiowaniu
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity';
            toast.textContent = 'Kod dostępu skopiowany!';
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 2000);
        }).catch(function(err) {
            console.error('Błąd kopiowania: ', err);
            alert('Nie udało się skopiować kodu');
        });
    }

    function regenerateAccessCode(doctorId, doctorName) {
        if (confirm('Czy na pewno chcesz wygenerować nowy kod dostępu dla lekarza "' + doctorName + '"? Stary kod przestanie działać.')) {
            // Pokaż spinner
            const spinner = document.createElement('div');
            spinner.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            spinner.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generowanie nowego kodu...';
            document.body.appendChild(spinner);

            fetch('/admin/lekarze/regenerate-code/' + doctorId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    document.body.removeChild(spinner);

                    if (data.success) {
                        // Pokaż komunikat sukcesu
                        const toast = document.createElement('div');
                        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-opacity';
                        toast.textContent = 'Nowy kod dostępu: ' + data.access_code;
                        document.body.appendChild(toast);

                        setTimeout(() => {
                            toast.style.opacity = '0';
                            setTimeout(() => document.body.removeChild(toast), 300);
                        }, 5000);

                        // Odśwież stronę po 2 sekundach
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    } else {
                        alert('Błąd: ' + (data.error || 'Nie udało się wygenerować nowego kodu'));
                    }
                })
                .catch(error => {
                    document.body.removeChild(spinner);
                    console.error('Błąd:', error);
                    alert('Wystąpił błąd podczas generowania kodu');
                });
        }
    }

    // Funkcja wyszukiwania
    function filterDoctors() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const doctorCards = document.querySelectorAll('.doctor-card');
        let visibleCount = 0;

        doctorCards.forEach(card => {
            const name = card.dataset.name || '';
            const specialization = card.dataset.specialization || '';

            const isVisible = name.includes(searchTerm) || specialization.includes(searchTerm);

            if (isVisible) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // Pokaż komunikat jeśli brak wyników
        let noResultsMsg = document.getElementById('noResultsMessage');
        if (visibleCount === 0 && searchTerm.length > 0) {
            if (!noResultsMsg) {
                noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'noResultsMessage';
                noResultsMsg.className = 'col-span-full text-center py-8 text-slate-500';
                noResultsMsg.innerHTML = `
                    <span class="material-icons-outlined text-4xl mb-2 block">search_off</span>
                    <p>Nie znaleziono lekarzy pasujących do wyszukiwania</p>
                `;
                document.getElementById('doctorsGrid').appendChild(noResultsMsg);
            }
            noResultsMsg.style.display = 'block';
        } else if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }

    // Dodaj nasłuchiwanie na pole wyszukiwania
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', filterDoctors);
            searchInput.addEventListener('keyup', filterDoctors);
        }
    });
</script>