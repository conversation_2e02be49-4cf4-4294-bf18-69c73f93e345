<?php
$pageTitle = 'Materiały video - ' . htmlspecialchars($category['name']);
?>

<div class="min-h-screen bg-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center gap-4 mb-4">
                <a href="/admin/video" class="p-2 text-slate-400 hover:text-slate-600 transition-colors">
                    <span class="material-icons-outlined">arrow_back</span>
                </a>
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-slate-900">
                        Materiały video: <?= htmlspecialchars($category['name']) ?>
                    </h1>
                    <?php if ($category['description']): ?>
                        <p class="text-slate-600 mt-1"><?= htmlspecialchars($category['description']) ?></p>
                    <?php endif; ?>
                </div>
                <div class="flex gap-3">
                    <a href="/admin/video/edit-category?id=<?= $category['id'] ?>"
                        class="inline-flex items-center px-4 py-2 bg-slate-600 text-white text-sm font-medium rounded-lg hover:bg-slate-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">edit</span>
                        Edytuj kategorię
                    </a>
                    <a href="/admin/video/all"
                        class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">video_library</span>
                        Wszystkie video
                    </a>
                </div>
            </div>

            <!-- Informacje o kategorii -->
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-6">
                        <div class="flex items-center gap-2">
                            <span class="material-icons-outlined text-slate-400">video_library</span>
                            <span class="text-sm text-slate-600">Materiały video:</span>
                            <span class="font-medium text-slate-900"><?= count($videos) ?></span>
                        </div>
                        <?php
                        $pendingCount = array_filter($videos, fn($v) => $v['approval_status'] === 'pending');
                        $approvedCount = array_filter($videos, fn($v) => $v['approval_status'] === 'approved');
                        $rejectedCount = array_filter($videos, fn($v) => $v['approval_status'] === 'rejected');
                        ?>
                        <?php if (count($pendingCount) > 0): ?>
                            <div class="flex items-center gap-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <?= count($pendingCount) ?> oczekujące
                                </span>
                            </div>
                        <?php endif; ?>
                        <?php if (count($approvedCount) > 0): ?>
                            <div class="flex items-center gap-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <?= count($approvedCount) ?> zaakceptowane
                                </span>
                            </div>
                        <?php endif; ?>
                        <?php if (count($rejectedCount) > 0): ?>
                            <div class="flex items-center gap-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <?= count($rejectedCount) ?> odrzucone
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="flex items-center gap-2">
                        <span class="material-icons-outlined text-sm <?= $category['auto_accept'] ? 'text-green-600' : 'text-slate-400' ?>">
                            <?= $category['auto_accept'] ? 'check_circle' : 'radio_button_unchecked' ?>
                        </span>
                        <span class="text-sm <?= $category['auto_accept'] ? 'text-green-800' : 'text-slate-600' ?>">
                            <?= $category['auto_accept'] ? 'Auto-akceptacja włączona' : 'Auto-akceptacja wyłączona' ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtry -->
        <div class="mb-6 bg-white rounded-lg shadow-sm border border-slate-200 p-4">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-slate-700">Status:</label>
                    <select id="statusFilter" class="border border-slate-300 rounded-md px-3 py-1 text-sm">
                        <option value="">Wszystkie</option>
                        <option value="pending">Oczekujące</option>
                        <option value="approved">Zaakceptowane</option>
                        <option value="rejected">Odrzucone</option>
                    </select>
                </div>
                <div class="flex items-center gap-2">
                    <input type="text" id="searchInput" placeholder="Szukaj po nazwie..."
                        class="border border-slate-300 rounded-md px-3 py-1 text-sm w-64">
                </div>
                <?php if (count($pendingCount) > 0): ?>
                    <div class="ml-auto">
                        <form method="POST" action="/admin/video/approve-category" class="inline"
                            onsubmit="return confirm('Czy na pewno chcesz zaakceptować wszystkie oczekujące materiały w tej kategorii?')">
                            <input type="hidden" name="id" value="<?= $category['id'] ?>">
                            <button type="submit"
                                class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                <span class="material-icons-outlined text-sm mr-2">check_circle</span>
                                Zaakceptuj wszystkie oczekujące
                            </button>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Lista materiałów video -->
        <?php if (empty($videos)): ?>
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
                <div class="text-slate-400 mb-4">
                    <span class="material-icons-outlined text-6xl">video_library</span>
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">Brak materiałów video</h3>
                <p class="text-slate-600">W tej kategorii nie ma jeszcze żadnych materiałów video.</p>
            </div>
        <?php else: ?>
            <div class="bg-white rounded-lg shadow-sm border border-slate-200">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-slate-50 border-b border-slate-200">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Materiał</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Reklamodawca</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">Data</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">Akcje</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-slate-200" id="videosTable">
                            <?php foreach ($videos as $video): ?>
                                <tr class="video-row"
                                    data-status="<?= htmlspecialchars($video['approval_status']) ?>"
                                    data-name="<?= htmlspecialchars(strtolower($video['name'])) ?>">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-12 w-12">
                                                <button onclick="showVideoPreview('<?= htmlspecialchars($video['id']) ?>', '<?= htmlspecialchars($video['name']) ?>', '<?= htmlspecialchars($video['media_url']) ?>', '<?= htmlspecialchars($video['media_type']) ?>', '<?= htmlspecialchars($video['youtube_id'] ?? '') ?>')"
                                                    class="h-12 w-12 bg-slate-100 rounded-lg flex items-center justify-center hover:bg-slate-200 transition-colors cursor-pointer"
                                                    title="Podgląd video">
                                                    <span class="material-icons-outlined text-slate-400">play_circle</span>
                                                </button>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-slate-900 truncate max-w-xs" title="<?= htmlspecialchars($video['name']) ?>">
                                                    <?= htmlspecialchars($video['name']) ?>
                                                </div>
                                                <div class="text-sm text-slate-500 truncate max-w-xs" title="<?= htmlspecialchars($video['media_url']) ?>">
                                                    <?= htmlspecialchars($video['media_url']) ?>
                                                </div>
                                                <div class="text-xs text-slate-400">
                                                    Czas: <?= $video['duration'] ?>s
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <?php
                                        $statusColors = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'approved' => 'bg-green-100 text-green-800',
                                            'rejected' => 'bg-red-100 text-red-800'
                                        ];
                                        $statusLabels = [
                                            'pending' => 'Oczekuje',
                                            'approved' => 'Zaakceptowany',
                                            'rejected' => 'Odrzucony'
                                        ];
                                        $statusClass = $statusColors[$video['approval_status']] ?? 'bg-slate-100 text-slate-800';
                                        $statusLabel = $statusLabels[$video['approval_status']] ?? $video['approval_status'];
                                        ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                                            <?= $statusLabel ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-slate-900">
                                        <?= htmlspecialchars($video['ads_advertiser_name'] ?? 'Brak') ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-slate-500">
                                        <?= date('d.m.Y H:i', strtotime($video['created_at'])) ?>
                                    </td>
                                    <td class="px-6 py-4 text-right text-sm font-medium">
                                        <div class="flex items-center justify-end gap-2">
                                            <button onclick="showEditModal('<?= $video['id'] ?>')" class="text-blue-600 hover:text-blue-900 p-1" title="Edytuj">
                                                <span class="material-icons-outlined text-sm">edit</span>
                                            </button>

                                            <?php if ($video['approval_status'] !== 'approved'): ?>
                                                <form method="POST" action="/admin/video/approve" class="inline"
                                                    onsubmit="return confirm('Czy na pewno chcesz zaakceptować ten materiał video?')">
                                                    <input type="hidden" name="id" value="<?= $video['id'] ?>">
                                                    <button type="submit" class="text-green-600 hover:text-green-900 p-1" title="Zaakceptuj">
                                                        <span class="material-icons-outlined text-sm">check_circle</span>
                                                    </button>
                                                </form>
                                            <?php endif; ?>

                                            <?php if ($video['approval_status'] !== 'rejected'): ?>
                                                <form method="POST" action="/admin/video/reject" class="inline"
                                                    onsubmit="return confirm('Czy na pewno chcesz odrzucić ten materiał video?')">
                                                    <input type="hidden" name="id" value="<?= $video['id'] ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-900 p-1" title="Odrzuć">
                                                        <span class="material-icons-outlined text-sm">cancel</span>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    // Filtrowanie materiałów video
    document.addEventListener('DOMContentLoaded', function() {
        const statusFilter = document.getElementById('statusFilter');
        const searchInput = document.getElementById('searchInput');
        const videoRows = document.querySelectorAll('.video-row');

        function filterVideos() {
            const statusValue = statusFilter.value;
            const searchValue = searchInput.value.toLowerCase();

            videoRows.forEach(row => {
                const status = row.dataset.status;
                const name = row.dataset.name;

                const statusMatch = !statusValue || status === statusValue;
                const nameMatch = !searchValue || name.includes(searchValue);

                if (statusMatch && nameMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        statusFilter.addEventListener('change', filterVideos);
        searchInput.addEventListener('input', filterVideos);
    });

    // Funkcje podglądu video
    function showVideoPreview(id, name, mediaUrl, mediaType, youtubeId) {
        document.getElementById('videoPreviewTitle').textContent = 'Podgląd: ' + name;
        const content = document.getElementById('videoPreviewContent');

        if (mediaType === 'youtube' && youtubeId) {
            content.innerHTML = `
                <div class="w-full h-full relative" style="aspect-ratio: 16/9;">
                    <iframe
                        class="absolute inset-0 w-full h-full rounded-lg"
                        src="https://www.youtube.com/embed/${youtubeId}"
                        frameborder="0"
                        allowfullscreen>
                    </iframe>
                </div>`;
        } else if (mediaType === 'video') {
            content.innerHTML = `
                <video
                    class="w-full h-full max-h-full object-contain rounded-lg"
                    controls
                    style="max-height: 70vh;">
                    <source src="${mediaUrl}" type="video/mp4">
                    Twoja przeglądarka nie obsługuje video.
                </video>`;
        } else if (mediaType === 'image') {
            content.innerHTML = `
                <img
                    src="${mediaUrl}"
                    alt="${name}"
                    class="max-w-full max-h-full object-contain rounded-lg"
                    style="max-height: 70vh;">`;
        } else {
            content.innerHTML = '<p class="text-slate-500">Podgląd niedostępny dla tego typu pliku</p>';
        }

        document.getElementById('videoPreviewModal').classList.remove('hidden');
        document.getElementById('videoPreviewModal').classList.add('flex');
    }

    function hideVideoPreview() {
        document.getElementById('videoPreviewModal').classList.add('hidden');
        document.getElementById('videoPreviewModal').classList.remove('flex');
        document.getElementById('videoPreviewContent').innerHTML = '';
    }



    // Funkcje edycji video
    function showEditModal(videoId) {
        // Pobierz dane video przez AJAX
        fetch(`/admin/video/get-video-data?id=${videoId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const video = data.video;
                    document.getElementById('editVideoId').value = video.id;
                    document.getElementById('editVideoName').value = video.name;
                    document.getElementById('editVideoDescription').value = video.description || '';
                    document.getElementById('editMediaType').value = video.media_type;
                    document.getElementById('editVideoCategory').value = video.category_id || '';
                    document.getElementById('editAdvertiser').value = video.ads_advertiser_id || '';
                    document.getElementById('editDisplayDuration').value = video.display_duration || 30;

                    if (video.youtube_id) {
                        document.getElementById('editYoutubeUrl').value = `https://www.youtube.com/watch?v=${video.youtube_id}`;
                    }

                    toggleMediaFields();

                    document.getElementById('editVideoModal').classList.remove('hidden');
                    document.getElementById('editVideoModal').classList.add('flex');
                } else {
                    alert('Błąd podczas ładowania danych video');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Błąd podczas ładowania danych video');
            });
    }

    function hideEditModal() {
        document.getElementById('editVideoModal').classList.add('hidden');
        document.getElementById('editVideoModal').classList.remove('flex');
    }

    function toggleMediaFields() {
        const mediaType = document.getElementById('editMediaType').value;
        const youtubeField = document.getElementById('youtubeUrlField');
        const localFileField = document.getElementById('localFileField');
        const durationField = document.getElementById('displayDurationField');

        youtubeField.classList.add('hidden');
        localFileField.classList.add('hidden');
        durationField.classList.add('hidden');

        if (mediaType === 'youtube') {
            youtubeField.classList.remove('hidden');
        } else if (mediaType === 'video' || mediaType === 'image') {
            localFileField.classList.remove('hidden');
            if (mediaType === 'image') {
                durationField.classList.remove('hidden');
            }
        }
    }

    function loadAdvertisers() {
        fetch('/admin/video/get-advertisers')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const select = document.getElementById('editAdvertiser');
                    select.innerHTML = '<option value="">Brak reklamodawcy</option>';
                    data.advertisers.forEach(advertiser => {
                        const option = document.createElement('option');
                        option.value = advertiser.id;
                        option.textContent = advertiser.name;
                        select.appendChild(option);
                    });
                }
            })
            .catch(error => console.error('Error loading advertisers:', error));
    }

    // Zamknij modal po naciśnięciu ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideVideoPreview();
            hideEditModal();
        }
    });

    // Zamknij modals po kliknięciu w tło
    document.getElementById('videoPreviewModal').addEventListener('click', function(e) {
        if (e.target === this) hideVideoPreview();
    });

    document.getElementById('editVideoModal').addEventListener('click', function(e) {
        if (e.target === this) hideEditModal();
    });

    // Załaduj reklamodawców przy starcie
    document.addEventListener('DOMContentLoaded', function() {
        loadAdvertisers();
    });
</script>

<!-- Modal podglądu video -->
<div id="videoPreviewModal" class="fixed inset-0 bg-black bg-opacity-75 hidden items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col">
        <div class="px-6 py-4 border-b border-slate-200 flex justify-between items-center flex-shrink-0">
            <h3 id="videoPreviewTitle" class="text-lg font-medium text-slate-900 truncate">Podgląd video</h3>
            <button onclick="hideVideoPreview()" class="text-slate-400 hover:text-slate-600 p-1 hover:bg-slate-100 rounded">
                <span class="material-icons-outlined">close</span>
            </button>
        </div>
        <div class="flex-1 p-6 overflow-hidden">
            <div id="videoPreviewContent" class="w-full h-full min-h-[400px] bg-slate-100 rounded-lg flex items-center justify-center">
                <!-- Zawartość video będzie wstawiona tutaj -->
            </div>
        </div>
    </div>
</div>

<!-- Modal edycji video -->
<div id="editVideoModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-slate-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-slate-900">Edytuj materiał video</h3>
            <button onclick="hideEditModal()" class="text-slate-400 hover:text-slate-600">
                <span class="material-icons-outlined">close</span>
            </button>
        </div>
        <form id="editVideoForm" method="POST" action="/admin/video/update" enctype="multipart/form-data">
            <div class="px-6 py-4 space-y-4">
                <input type="hidden" id="editVideoId" name="id">

                <!-- Nazwa -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Nazwa materiału</label>
                    <input type="text" id="editVideoName" name="name" required
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Opis -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Opis</label>
                    <textarea id="editVideoDescription" name="description" rows="3"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                </div>

                <!-- Typ mediów -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Typ materiału</label>
                    <select id="editMediaType" name="media_type" onchange="toggleMediaFields()"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="video">Video (plik lokalny)</option>
                        <option value="youtube">YouTube</option>
                        <option value="image">Zdjęcie</option>
                    </select>
                </div>

                <!-- URL YouTube -->
                <div id="youtubeUrlField" class="hidden">
                    <label class="block text-sm font-medium text-slate-700 mb-1">Link YouTube</label>
                    <input type="url" id="editYoutubeUrl" name="youtube_url"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="https://www.youtube.com/watch?v=...">
                </div>

                <!-- Plik lokalny -->
                <div id="localFileField">
                    <label class="block text-sm font-medium text-slate-700 mb-1">Plik lokalny</label>
                    <input type="file" id="editLocalFile" name="local_file" accept="video/*,image/*"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    <p class="text-xs text-slate-500 mt-1">Zostaw puste aby zachować obecny plik</p>
                </div>

                <!-- Czas wyświetlania (dla zdjęć) -->
                <div id="displayDurationField" class="hidden">
                    <label class="block text-sm font-medium text-slate-700 mb-1">Czas wyświetlania (sekundy)</label>
                    <input type="number" id="editDisplayDuration" name="display_duration" min="1" max="300" value="30"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <!-- Kategoria -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Kategoria</label>
                    <select id="editVideoCategory" name="category_id"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Brak kategorii</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Reklamodawca -->
                <div>
                    <label class="block text-sm font-medium text-slate-700 mb-1">Reklamodawca</label>
                    <select id="editAdvertiser" name="ads_advertiser_id"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">Brak reklamodawcy</option>
                        <!-- Opcje będą załadowane przez JavaScript -->
                    </select>
                </div>
            </div>
            <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-end gap-3">
                <button type="button" onclick="hideEditModal()"
                    class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors">
                    Anuluj
                </button>
                <button type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 transition-colors">
                    Zapisz zmiany
                </button>
            </div>
        </form>
    </div>
</div>