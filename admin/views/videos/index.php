<?php
$pageTitle = 'Kategorie materiałów video';
?>

<div class="min-h-screen bg-slate-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-slate-900">Kategorie materiałów video</h1>
                    <p class="text-slate-600 mt-1">Zarządzaj kategoriami i materiałami video</p>
                </div>
                <div class="flex gap-3">
                    <a href="/admin/video/all" class="inline-flex items-center px-4 py-2 bg-slate-600 text-white text-sm font-medium rounded-lg hover:bg-slate-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">video_library</span>
                        Wszystkie video
                    </a>
                    <a href="/admin/video/add" class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">video_call</span>
                        Dodaj materiał
                    </a>
                    <button onclick="showAddCategoryModal()" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                        <span class="material-icons-outlined text-sm mr-2">add</span>
                        Dodaj kategorię
                    </button>
                </div>
            </div>
        </div>

        <!-- Komunikaty -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['success']) ?>
                <?php unset($_SESSION['success']); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                <?= htmlspecialchars($_SESSION['error']) ?>
                <?php unset($_SESSION['error']); ?>
            </div>
        <?php endif; ?>

        <!-- Lista kategorii -->
        <?php if (empty($categories)): ?>
            <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-8 text-center">
                <div class="text-slate-400 mb-4">
                    <span class="material-icons-outlined text-6xl">category</span>
                </div>
                <h3 class="text-lg font-medium text-slate-900 mb-2">Brak kategorii</h3>
                <p class="text-slate-600 mb-4">Nie znaleziono żadnych kategorii materiałów video.</p>
                <button onclick="showAddCategoryModal()" class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-lg hover:bg-indigo-700 transition-colors">
                    <span class="material-icons-outlined text-sm mr-2">add</span>
                    Dodaj pierwszą kategorię
                </button>
            </div>
        <?php else: ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><?php foreach ($categories as $category): ?>
                    <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow">
                        <!-- Header kategorii -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1 min-w-0">
                                <h3 class="text-lg font-semibold text-slate-900 truncate" title="<?= htmlspecialchars($category['name']) ?>">
                                    <?= htmlspecialchars($category['name']) ?>
                                </h3>
                                <div class="text-sm text-slate-600 mt-1 h-10 flex flex-col justify-start">
                                    <?php
                                                                                    $description = $category['description'] ?? '';
                                                                                    $lines = explode("\n", wordwrap($description, 50, "\n", true));
                                    ?>
                                    <div class="leading-5" title="<?= htmlspecialchars($description) ?>">
                                        <?= htmlspecialchars($lines[0] ?? '') ?>
                                    </div>
                                    <div class="leading-5" title="<?= htmlspecialchars($description) ?>">
                                        <?= htmlspecialchars($lines[1] ?? '') ?>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-1 ml-3">
                                <a href="/admin/video/edit-category?id=<?= $category['id'] ?>" class="p-1 text-slate-400 hover:text-indigo-600 transition-colors" title="Edytuj kategorię">
                                    <span class="material-icons-outlined text-sm">edit</span>
                                </a>
                                <?php if ($category['video_count'] == 0): ?>
                                    <form method="POST" action="/admin/video/delete-category" class="inline" onsubmit="return confirm('Czy na pewno chcesz usunąć tę kategorię?')">
                                        <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                        <button type="submit" class="p-1 text-slate-400 hover:text-red-600 transition-colors" title="Usuń kategorię">
                                            <span class="material-icons-outlined text-sm">delete</span>
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Automatyczna akceptacja -->
                        <div class="mb-4">
                            <form method="POST" action="/admin/video/toggle-auto-accept" class="inline">
                                <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                <button type="submit" class="flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium transition-colors <?= $category['auto_accept'] ? 'bg-green-100 text-green-800 hover:bg-green-200' : 'bg-slate-100 text-slate-800 hover:bg-slate-200' ?>">
                                    <span class="material-icons-outlined text-sm">
                                        <?= $category['auto_accept'] ? 'check_circle' : 'radio_button_unchecked' ?>
                                    </span>
                                    <?= $category['auto_accept'] ? 'Auto-akceptacja włączona' : 'Auto-akceptacja wyłączona' ?>
                                </button>
                            </form>
                        </div>

                        <!-- Statystyki -->
                        <div class="mb-4 space-y-2">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Materiały video:</span>
                                <span class="font-medium text-slate-900"><?= $category['video_count'] ?></span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Oczekujące:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['pending_count'] > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['pending_count'] ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Zaakceptowane:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['approved_count'] > 0 ? 'bg-green-100 text-green-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['approved_count'] ?>
                                </span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-slate-600">Odrzucone:</span>
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium <?= $category['rejected_count'] > 0 ? 'bg-red-100 text-red-800' : 'bg-slate-100 text-slate-600' ?>">
                                    <?= $category['rejected_count'] ?>
                                </span>
                            </div>
                        </div>

                        <!-- Akcje -->
                        <div class="flex gap-2">
                            <a href="/admin/video/category?id=<?= $category['id'] ?>" class="flex-1 inline-flex items-center justify-center px-3 py-2 bg-slate-100 text-slate-700 text-sm font-medium rounded-lg hover:bg-slate-200 transition-colors">
                                <span class="material-icons-outlined text-sm mr-1">visibility</span>
                                Zobacz video
                            </a>
                            <?php if ($category['pending_count'] > 0): ?>
                                <form method="POST" action="/admin/video/approve-category" class="flex-1" onsubmit="return confirm('Czy na pewno chcesz zaakceptować wszystkie oczekujące materiały w tej kategorii?')">
                                    <input type="hidden" name="id" value="<?= $category['id'] ?>">
                                    <button type="submit" class="w-full inline-flex items-center justify-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                        <span class="material-icons-outlined text-sm mr-1">check_circle</span>
                                        Zaakceptuj
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>

                        <!-- Data utworzenia -->
                        <div class="mt-3 pt-3 border-t border-slate-100">
                            <p class="text-xs text-slate-500">
                                Utworzono: <?= date('d.m.Y H:i', strtotime($category['created_at'])) ?>
                            </p>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Informacje o automatycznej akceptacji -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <span class="material-icons-outlined text-blue-600 mr-3 mt-0.5">info</span>
                <div>
                    <h3 class="text-sm font-medium text-blue-900 mb-1">Automatyczna akceptacja</h3>
                    <p class="text-sm text-blue-800">
                        Materiały video przypisane do kategorii z włączoną automatyczną akceptacją będą automatycznie zaakceptowane po przesłaniu.
                        Kategorie bez automatycznej akceptacji wymagają ręcznego zatwierdzenia każdego materiału.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal dodawania kategorii -->
<div id="addCategoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4 border-b border-slate-200">
            <h3 class="text-lg font-medium text-slate-900">Dodaj nową kategorię</h3>
        </div>
        <form method="POST" action="/admin/video/add-category">
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label for="categoryName" class="block text-sm font-medium text-slate-700 mb-1">Nazwa kategorii</label>
                    <input type="text" id="categoryName" name="name" required
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="np. Edukacja medyczna">
                </div>
                <div>
                    <label for="categoryDescription" class="block text-sm font-medium text-slate-700 mb-1">Opis (opcjonalny)</label>
                    <textarea id="categoryDescription" name="description" rows="3"
                        class="w-full border border-slate-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="Opis kategorii..."></textarea>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="autoAccept" name="auto_accept" value="1"
                        class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-300 rounded">
                    <label for="autoAccept" class="ml-2 block text-sm text-slate-700">
                        Automatyczna akceptacja materiałów w tej kategorii
                    </label>
                </div>
            </div>
            <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-end gap-3">
                <button type="button" onclick="hideAddCategoryModal()"
                    class="px-4 py-2 text-sm font-medium text-slate-700 bg-white border border-slate-300 rounded-md hover:bg-slate-50 transition-colors">
                    Anuluj
                </button>
                <button type="submit"
                    class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 transition-colors">
                    Dodaj kategorię
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function showAddCategoryModal() {
        document.getElementById('addCategoryModal').classList.remove('hidden');
        document.getElementById('addCategoryModal').classList.add('flex');
        document.getElementById('categoryName').focus();
    }

    function hideAddCategoryModal() {
        document.getElementById('addCategoryModal').classList.add('hidden');
        document.getElementById('addCategoryModal').classList.remove('flex');
        // Wyczyść formularz
        document.getElementById('categoryName').value = '';
        document.getElementById('categoryDescription').value = '';
        document.getElementById('autoAccept').checked = false;
    }

    // Zamknij modal po kliknięciu w tło
    document.getElementById('addCategoryModal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideAddCategoryModal();
        }
    });

    // Zamknij modal po naciśnięciu ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAddCategoryModal();
        }
    });
</script>