<?php

/**
 * DoctorController - zarząd<PERSON>ie lekarzami
 */
class DoctorController {
    private $db;
    private $doctorModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->doctorModel = new Doctor();
    }

    /**
     * Lista lekarzy
     */
    public function index() {
        $doctors = $this->doctorModel->getAllForClient();

        $this->render('doctors/index', [
            'doctors' => $doctors,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Formularz dodawania lekarza
     */
    public function create() {
        $this->render('doctors/create', [
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowego lekarza
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $photoUrl = trim($_POST['photo_url'] ?? '');

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/create');
            exit;
        }

        try {
            $this->doctorModel->create($firstName, $lastName, $specialization, $photoUrl);
            $_SESSION['success'] = 'Lekarz został dodany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas dodawania lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/create');
        }
        exit;
    }

    /**
     * Formularz edycji lekarza
     */
    public function edit() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $doctor = $this->doctorModel->getById($id);
        if (!$doctor) {
            $_SESSION['error'] = 'Lekarz nie został znaleziony';
            header('Location: /admin/doctors');
            exit;
        }

        $this->render('doctors/edit', [
            'doctor' => $doctor,
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Aktualizuj lekarza
     */
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /admin/doctors');
            exit;
        }

        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        $firstName = trim($_POST['first_name'] ?? '');
        $lastName = trim($_POST['last_name'] ?? '');
        $specialization = trim($_POST['specialization'] ?? '');
        $photoUrl = trim($_POST['photo_url'] ?? '');

        if (empty($firstName) || empty($lastName)) {
            $_SESSION['error'] = 'Imię i nazwisko są wymagane';
            header('Location: /admin/doctors/edit/' . $id);
            exit;
        }

        try {
            $this->doctorModel->update($id, $firstName, $lastName, $specialization, $photoUrl);
            $_SESSION['success'] = 'Lekarz został zaktualizowany pomyślnie';
            header('Location: /admin/doctors');
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas aktualizacji lekarza: ' . $e->getMessage();
            header('Location: /admin/doctors/edit/' . $id);
        }
        exit;
    }

    /**
     * Usuń lekarza
     */
    public function delete() {
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /admin/doctors');
            exit;
        }

        try {
            $this->doctorModel->delete($id);
            $_SESSION['success'] = 'Lekarz został usunięty pomyślnie';
        } catch (Exception $e) {
            $_SESSION['error'] = 'Błąd podczas usuwania lekarza: ' . $e->getMessage();
        }

        header('Location: /admin/doctors');
        exit;
    }

    /**
     * Regeneruj kod dostępu lekarza
     */
    public function regenerateCode() {
        header('Content-Type: application/json');

        $id = $_GET['id'] ?? null;
        if (!$id) {
            echo json_encode(['success' => false, 'error' => 'Brak ID lekarza']);
            exit;
        }

        try {
            $newAccessCode = $this->doctorModel->regenerateAccessCode($id);
            if ($newAccessCode) {
                echo json_encode(['success' => true, 'access_code' => $newAccessCode]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Nie udało się wygenerować nowego kodu']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * API - statystyki lekarza
     */
    public function getStats() {
        $doctorId = $_GET['doctor_id'] ?? null;
        $date = $_GET['date'] ?? date('Y-m-d');

        if (!$doctorId) {
            http_response_code(400);
            echo json_encode(['error' => 'Brak ID lekarza']);
            exit;
        }

        try {
            $stats = $this->doctorModel->getStats($doctorId, $date);
            echo json_encode(['success' => true, 'stats' => $stats]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
        exit;
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }
}
