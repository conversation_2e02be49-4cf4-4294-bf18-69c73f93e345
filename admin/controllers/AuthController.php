<?php

/**
 * AuthController - obsługa logowania i wylogowania
 */
class AuthController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Strona logowania
     */
    public function login() {
        // Jeśli użytkownik jest już zalogowany, przekieruj na pulpit
        if (isLoggedIn()) {
            redirect('/admin/pulpit');
        }

        $error = null;

        // Obsługa formularza logowania
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $email = trim($_POST['email'] ?? '');
            $password = $_POST['password'] ?? '';

            if (empty($email) || empty($password)) {
                $error = 'Email i hasło są wymagane';
            } else {
                $user = $this->authenticateUser($email, $password);

                if ($user) {
                    // Zaloguj użytkownika
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['username']; // UWAGA: Kolumna company_name została usunięta
                    $_SESSION['login_time'] = time();

                    // Aktualizuj ostatnią aktywność
                    $this->updateLastActivity($user['id']);

                    // Przekieruj na pulpit
                    redirect('/admin/pulpit');
                } else {
                    $error = 'Nieprawidłowy email lub hasło';
                }
            }
        }

        // Wyświetl formularz logowania
        $this->renderLoginForm($error);
    }

    /**
     * Wylogowanie użytkownika
     */
    public function logout() {
        // Wyczyść sesję
        session_unset();
        session_destroy();

        // Rozpocznij nową sesję
        session_start();

        // Przekieruj na stronę logowania
        redirect('/admin/login');
    }

    /**
     * Uwierzytelnienie użytkownika
     */
    private function authenticateUser($email, $password) {
        $sql = "SELECT * FROM users WHERE email = ?"; // UWAGA: Kolumna is_active została usunięta
        $user = $this->db->fetchOne($sql, [$email]);

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }

    /**
     * Aktualizuj ostatnią aktywność użytkownika
     */
    private function updateLastActivity($userId) {
        $sql = "UPDATE users SET last_activity = datetime('now') WHERE id = ?";
        $this->db->execute($sql, [$userId]);
    }

    /**
     * Renderuj formularz logowania
     */
    private function renderLoginForm($error = null) {
?>
        <!DOCTYPE html>
        <html lang="pl">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Logowanie - <?= APP_NAME ?></title>

            <!-- Tailwind CSS -->
            <script src="https://cdn.tailwindcss.com"></script>

            <!-- Fonts -->
            <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin />
            <link rel="stylesheet" href="https://fonts.googleapis.com/css2?display=swap&family=Inter:wght@400;500;600;700;900" />

            <!-- Icons -->
            <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">

            <style>
                body {
                    font-family: 'Inter', sans-serif;
                }
            </style>
        </head>

        <body class="bg-slate-50 min-h-screen flex items-center justify-center">
            <div class="max-w-md w-full space-y-8 p-8">
                <div class="text-center">
                    <div class="mx-auto h-16 w-16 bg-indigo-600 rounded-lg flex items-center justify-center mb-4">
                        <span class="material-icons-outlined text-white text-2xl">medical_services</span>
                    </div>
                    <h2 class="text-3xl font-bold text-slate-900"><?= APP_NAME ?></h2>
                    <p class="mt-2 text-sm text-slate-600">System zarządzania kolejkami</p>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
                    <form method="POST" class="space-y-6">
                        <?php if ($error): ?>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                <div class="flex">
                                    <span class="material-icons-outlined text-red-400 text-sm mr-2">error</span>
                                    <span class="text-sm text-red-800"><?= htmlspecialchars($error) ?></span>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div>
                            <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                                Email
                            </label>
                            <input type="email" id="email" name="email" required
                                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Wprowadź swój email"
                                value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                        </div>

                        <div>
                            <label for="password" class="block text-sm font-medium text-slate-700 mb-2">
                                Hasło
                            </label>
                            <input type="password" id="password" name="password" required
                                class="w-full px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Wprowadź swoje hasło">
                        </div>

                        <button type="submit"
                            class="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors font-medium">
                            Zaloguj się
                        </button>
                    </form>
                </div>

                <div class="text-center">
                    <p class="text-xs text-slate-500">
                        <?= APP_NAME ?> v<?= APP_VERSION ?> &copy; <?= date('Y') ?>
                    </p>
                </div>
            </div>
        </body>

        </html>
<?php
    }
}

?>