<?php

/**
 * Controller do zarządzania wyświetlaczami
 */
class DisplayController {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance()->getPdo();
    }

    /**
     * Lista wyświetlaczy
     */
    public function index() {
        $displays = $this->getDisplays();

        $this->render('displays/index', [
            'displays' => $displays,
            'title' => 'Zarządzanie wyświetlaczami',
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Sparuj wyświetlacz z kodem
     */
    public function pair() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $pairingCode = trim($_POST['pairing_code'] ?? '');

        if (empty($pairingCode)) {
            $_SESSION['error'] = 'Kod parowania jest wymagany';
            $this->redirect('/admin/wyswietlacze');
        }

        // Znajdź wyświetlacz z tym kodem parowania
        $stmt = $this->db->prepare("
            SELECT * FROM client_displays 
            WHERE pairing_code = ? AND pairing_status = 'pending'
            AND pairing_expires_at > datetime('now')
        ");
        $stmt->execute([strtolower($pairingCode)]);
        $display = $stmt->fetch();

        if (!$display) {
            $_SESSION['error'] = 'Nieprawidłowy lub wygasły kod parowania';
            $this->redirect('/admin/wyswietlacze');
        }

        // Sparuj wyświetlacz
        $stmt = $this->db->prepare("
            UPDATE client_displays 
            SET pairing_status = 'paired', paired_at = datetime('now'),
                pairing_code = NULL, pairing_expires_at = NULL
            WHERE id = ?
        ");

        if ($stmt->execute([$display['id']])) {
            $_SESSION['success'] = 'Wyświetlacz "' . $display['display_name'] . '" został pomyślnie sparowany';
        } else {
            $_SESSION['error'] = 'Błąd podczas parowania wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Usuń parowanie wyświetlacza
     */
    public function unpair() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $displayId = $_POST['display_id'] ?? 0;

        if (!$displayId) {
            $_SESSION['error'] = 'Nieprawidłowy ID wyświetlacza';
            $this->redirect('/admin/wyswietlacze');
        }

        $stmt = $this->db->prepare("
            UPDATE client_displays 
            SET pairing_status = 'unpaired', paired_at = NULL,
                pairing_code = NULL, pairing_expires_at = NULL
            WHERE id = ?
        ");

        if ($stmt->execute([$displayId])) {
            $_SESSION['success'] = 'Parowanie wyświetlacza zostało usunięte';
        } else {
            $_SESSION['error'] = 'Błąd podczas usuwania parowania';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Dodaj nowy wyświetlacz
     */
    public function create() {
        $this->render('displays/create', [
            'title' => 'Dodaj wyświetlacz',
            'user' => getCurrentUser()
        ]);
    }

    /**
     * Zapisz nowy wyświetlacz
     */
    public function store() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $displayName = trim($_POST['display_name'] ?? '');

        if (empty($displayName)) {
            $_SESSION['error'] = 'Nazwa wyświetlacza jest wymagana';
            $this->redirect('/admin/wyswietlacze/create');
        }

        // Wygeneruj unikalny kod wyświetlacza
        $displayCode = $this->generateDisplayCode();

        $stmt = $this->db->prepare("
            INSERT INTO client_displays (display_name, display_code, pairing_status, created_at)
            VALUES (?, ?, 'unpaired', datetime('now'))
        ");

        if ($stmt->execute([$displayName, $displayCode])) {
            $_SESSION['success'] = 'Wyświetlacz został dodany. Kod wyświetlacza: ' . $displayCode;
        } else {
            $_SESSION['error'] = 'Błąd podczas dodawania wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Usuń wyświetlacz
     */
    public function delete() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
        }

        $displayId = $_POST['display_id'] ?? 0;

        if (!$displayId) {
            $_SESSION['error'] = 'Nieprawidłowy ID wyświetlacza';
            $this->redirect('/admin/wyswietlacze');
        }

        $stmt = $this->db->prepare("DELETE FROM client_displays WHERE id = ?");

        if ($stmt->execute([$displayId])) {
            $_SESSION['success'] = 'Wyświetlacz został usunięty';
        } else {
            $_SESSION['error'] = 'Błąd podczas usuwania wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Pobierz wszystkie wyświetlacze
     */
    private function getDisplays() {
        $stmt = $this->db->prepare("
            SELECT *, 
                   CASE 
                       WHEN pairing_status = 'paired' THEN 'Sparowany'
                       WHEN pairing_status = 'pending' THEN 'Oczekuje na parowanie'
                       ELSE 'Nie sparowany'
                   END as status_text,
                   CASE 
                       WHEN is_online = 1 THEN 'Online'
                       ELSE 'Offline'
                   END as online_text
            FROM client_displays 
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Wygeneruj unikalny kod wyświetlacza
     */
    private function generateDisplayCode() {
        do {
            $code = '';
            $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
            for ($i = 0; $i < 6; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod już istnieje
            $stmt = $this->db->prepare("SELECT id FROM client_displays WHERE display_code = ?");
            $stmt->execute([$code]);
            $exists = $stmt->fetch();
        } while ($exists);

        return $code;
    }

    /**
     * Renderuj widok
     */
    private function render($view, $data = []) {
        extract($data);

        ob_start();
        include ADMIN_PATH . '/views/' . $view . '.php';
        $content = ob_get_clean();

        include ADMIN_PATH . '/views/layout.php';
    }

    /**
     * Aktualizuj nazwę wyświetlacza
     */
    public function updateName() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        $displayId = $_POST['display_id'] ?? null;
        $displayName = trim($_POST['display_name'] ?? '');

        if (!$displayId || !$displayName) {
            $_SESSION['error'] = 'Nieprawidłowe dane formularza';
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        // Sprawdź czy wyświetlacz istnieje
        $stmt = $this->db->prepare("SELECT id FROM client_displays WHERE id = ?");
        $stmt->execute([$displayId]);
        $display = $stmt->fetch();

        if (!$display) {
            $_SESSION['error'] = 'Wyświetlacz nie został znaleziony';
            $this->redirect('/admin/wyswietlacze');
            return;
        }

        // Aktualizuj nazwę
        $stmt = $this->db->prepare("UPDATE client_displays SET display_name = ? WHERE id = ?");

        if ($stmt->execute([$displayName, $displayId])) {
            $_SESSION['success'] = 'Nazwa wyświetlacza została zaktualizowana';
        } else {
            $_SESSION['error'] = 'Błąd podczas aktualizacji nazwy wyświetlacza';
        }

        $this->redirect('/admin/wyswietlacze');
    }

    /**
     * Przekieruj
     */
    private function redirect($url) {
        header('Location: ' . $url);
        exit;
    }
}
