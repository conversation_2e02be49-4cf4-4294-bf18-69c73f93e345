<?php

/**
 * Model Doctor - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>
 */
class Doctor {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkich lekarzy dla klienta
     */
    public function getAllForClient($clientId = CLIENT_ID_DOCTORS) {
        $sql = "
            SELECT
                d.*,
                r.name as default_room_name
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE d.client_id = ? AND d.active = 1
            ORDER BY d.last_name, d.first_name
        ";

        return $this->db->fetchAll($sql, [$clientId]);
    }

    /**
     * Pobierz lekarza po ID
     */
    public function getById($id) {
        $sql = "
            SELECT
                d.*,
                r.name as default_room_name
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            WHERE d.id = ?
        ";

        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * <PERSON><PERSON>rz lekarzy z dzisiej<PERSON>ymi wizytami
     */
    public function getDoctorsWithAppointments($date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT
                d.*,
                r.name as default_room_name,
                COUNT(a.id) as appointments_count,
                COUNT(CASE WHEN a.status = 'waiting' THEN 1 END) as waiting_count,
                COUNT(CASE WHEN a.status = 'current' THEN 1 END) as current_count,
                COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_count,
                MIN(CASE WHEN a.status = 'waiting' THEN a.appointment_time END) as next_appointment_time
            FROM queue_doctors d
            LEFT JOIN queue_rooms r ON d.default_room_id = r.id
            LEFT JOIN queue_appointments a ON d.id = a.doctor_id AND a.appointment_date = ?
            WHERE d.active = 1
            GROUP BY d.id
            ORDER BY COUNT(a.id) DESC, d.last_name, d.first_name
        ";

        return $this->db->fetchAll($sql, [$date]);
    }

    /**
     * Pobierz aktualną wizytę lekarza
     */
    public function getCurrentAppointment($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*, d.first_name, d.last_name
            FROM queue_appointments a
            JOIN queue_doctors d ON a.doctor_id = d.id
            WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'current'
            LIMIT 1
        ";

        return $this->db->fetchOne($sql, [$doctorId, $date]);
    }

    /**
     * Pobierz wszystkie wizyty lekarza (oczekujące, aktualne, zakończone)
     */
    public function getWaitingAppointments($doctorId, $date = null, $limit = 10) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*,
                CASE
                    WHEN a.appointment_time LIKE '%-%'
                    THEN substr(a.appointment_time, 12, 5)
                    ELSE substr(a.appointment_time, 1, 5)
                END as normalized_time
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ?
            ORDER BY normalized_time ASC
            LIMIT ?
        ";

        return $this->db->fetchAll($sql, [$doctorId, $date, $limit]);
    }

    /**
     * Pobierz wszystkie wizyty lekarza na dany dzień
     */
    public function getAllAppointments($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $sql = "
            SELECT a.*
            FROM queue_appointments a
            WHERE a.doctor_id = ? AND a.appointment_date = ?
            ORDER BY a.appointment_time ASC
        ";

        return $this->db->fetchAll($sql, [$doctorId, $date]);
    }

    /**
     * Oblicz opóźnienie lekarza
     */
    public function calculateDelay($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $currentTime = date('H:i:s');

        // Pobierz aktualną wizytę
        $currentAppointment = $this->getCurrentAppointment($doctorId, $date);

        if (!$currentAppointment) {
            // Sprawdź czy są oczekujące wizyty które już powinny się rozpocząć
            $sql = "
                SELECT a.*
                FROM queue_appointments a
                WHERE a.doctor_id = ? AND a.appointment_date = ? AND a.status = 'waiting'
                    AND a.appointment_time <= ?
                ORDER BY a.appointment_time ASC
                LIMIT 1
            ";

            $overdueAppointment = $this->db->fetchOne($sql, [$doctorId, $date, $currentTime]);

            if ($overdueAppointment) {
                // Oblicz opóźnienie na podstawie pierwszej zaległej wizyty
                $delayMinutes = calculateDelayMinutes($overdueAppointment['appointment_time'], $date, $overdueAppointment['status'] ?? 'waiting');

                // Dodaj czas oczekujących wizyt przed nią
                $waitingCount = $this->db->fetchCount("
                    SELECT COUNT(*)
                    FROM queue_appointments
                    WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
                        AND appointment_time < ?
                ", [$doctorId, $date, $overdueAppointment['appointment_time']]);

                $queueDelay = $waitingCount * DEFAULT_APPOINTMENT_DURATION;

                return $delayMinutes + $queueDelay;
            }

            return 0; // Brak opóźnienia
        }

        // Oblicz opóźnienie aktualnej wizyty w minutach
        $appointmentDelay = calculateDelayMinutes($currentAppointment['appointment_time'], $date, $currentAppointment['status'] ?? 'current');

        // Dodaj czas oczekujących wizyt
        $waitingCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        $queueDelay = $waitingCount * DEFAULT_APPOINTMENT_DURATION;

        return $appointmentDelay + $queueDelay;
    }

    /**
     * Pobierz status lekarza na podstawie wizyt
     */
    public function getStatus($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        // Sprawdź czy ma aktualną wizytę
        $currentAppointment = $this->getCurrentAppointment($doctorId, $date);
        if ($currentAppointment) {
            return 'working';
        }

        // Sprawdź czy ma oczekujące wizyty
        $waitingCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        if ($waitingCount > 0) {
            return 'ready';
        }

        // Sprawdź czy miał dziś jakieś wizyty
        $totalCount = $this->db->fetchCount("
            SELECT COUNT(*)
            FROM queue_appointments
            WHERE doctor_id = ? AND appointment_date = ?
        ", [$doctorId, $date]);

        if ($totalCount > 0) {
            return 'finished';
        }

        return 'no_appointments';
    }

    /**
     * Dodaj nowego lekarza
     */
    public function create($firstName, $lastName, $specialization = '', $photoUrl = '', $clientId = CLIENT_ID, $defaultRoomId = null) {
        // Wygeneruj unikalny kod dostępu
        $accessCode = $this->generateAccessCode();

        $sql = "
            INSERT INTO queue_doctors (client_id, first_name, last_name, specialization, photo_url, default_room_id, access_code, active, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, 1, datetime('now'))
        ";

        $this->db->execute($sql, [$clientId, $firstName, $lastName, $specialization, $photoUrl, $defaultRoomId, $accessCode]);
        return $this->db->lastInsertId();
    }

    /**
     * Aktualizuj lekarza
     */
    public function update($id, $firstName, $lastName, $specialization = '', $photoUrl = null, $defaultRoomId = null) {
        $sql = "
            UPDATE queue_doctors 
            SET first_name = ?, last_name = ?, specialization = ?, default_room_id = ?, updated_at = datetime('now')
        ";
        $params = [$firstName, $lastName, $specialization, $defaultRoomId];

        if ($photoUrl !== null) {
            $sql .= ", photo_url = ?";
            $params[] = $photoUrl;
        }

        $sql .= " WHERE id = ?";
        $params[] = $id;

        return $this->db->execute($sql, $params);
    }

    /**
     * Usuń lekarza (oznacz jako nieaktywny)
     */
    public function delete($id) {
        $sql = "UPDATE queue_doctors SET active = 0, updated_at = datetime('now') WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Pobierz statystyki lekarza
     */
    public function getStats($doctorId, $date = null) {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $stats = [];

        // Liczba wizyt dzisiaj
        $stats['appointments_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments 
            WHERE doctor_id = ? AND appointment_date = ?
        ", [$doctorId, $date]);

        // Liczba zakończonych wizyt
        $stats['completed_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments 
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'completed'
        ", [$doctorId, $date]);

        // Liczba oczekujących
        $stats['waiting_today'] = $this->db->fetchCount("
            SELECT COUNT(*) FROM queue_appointments 
            WHERE doctor_id = ? AND appointment_date = ? AND status = 'waiting'
        ", [$doctorId, $date]);

        // Opóźnienie
        $stats['delay_minutes'] = $this->calculateDelay($doctorId, $date);

        return $stats;
    }

    /**
     * Generuj unikalny 12-znakowy kod dostępu dla lekarza
     */
    private function generateAccessCode() {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyz';

        do {
            $code = '';
            // Generuj 12 znaków
            for ($i = 0; $i < 12; $i++) {
                $code .= $characters[random_int(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod już istnieje
            $existing = $this->db->fetchOne(
                "SELECT id FROM queue_doctors WHERE access_code = ?",
                [$code]
            );
        } while ($existing);

        return $code;
    }

    /**
     * Regeneruj kod dostępu dla lekarza
     */
    public function regenerateAccessCode($id) {
        $accessCode = $this->generateAccessCode();

        $sql = "UPDATE queue_doctors SET access_code = ?, updated_at = datetime('now') WHERE id = ?";
        $result = $this->db->execute($sql, [$accessCode, $id]);

        return $result ? $accessCode : false;
    }
}
