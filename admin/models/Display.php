<?php

/**
 * Model Display - zarządzanie wyświetlaczami
 */
class Display {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkie wyświetlacze
     */
    public function getAll() {
        $sql = "
            SELECT *, 
                   CASE 
                       WHEN pairing_status = 'paired' THEN 'Sparowany'
                       WHEN pairing_status = 'pending' THEN 'Oczekuje na parowanie'
                       ELSE 'Nie sparowany'
                   END as status_text,
                   CASE 
                       WHEN is_online = 1 THEN 'Online'
                       ELSE 'Offline'
                   END as online_text
            FROM client_displays 
            ORDER BY created_at DESC
        ";

        return $this->db->fetchAll($sql);
    }

    /**
     * Pobierz wyświetlacz po ID
     */
    public function getById($id) {
        $sql = "SELECT * FROM client_displays WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Pobierz wyświetlacz po kodzie
     */
    public function getByCode($code) {
        $sql = "SELECT * FROM client_displays WHERE display_code = ?";
        return $this->db->fetchOne($sql, [$code]);
    }

    /**
     * Pobierz wyświetlacz po kodzie parowania
     */
    public function getByPairingCode($pairingCode) {
        $sql = "
            SELECT * FROM client_displays 
            WHERE pairing_code = ? AND pairing_status = 'pending'
            AND pairing_expires_at > datetime('now')
        ";
        return $this->db->fetchOne($sql, [$pairingCode]);
    }

    /**
     * Dodaj nowy wyświetlacz
     */
    public function create($displayName) {
        $displayCode = $this->generateUniqueDisplayCode();
        
        $sql = "
            INSERT INTO client_displays (display_name, display_code, pairing_status, created_at)
            VALUES (?, ?, 'unpaired', datetime('now'))
        ";
        
        $this->db->execute($sql, [$displayName, $displayCode]);
        return $displayCode;
    }

    /**
     * Usuń wyświetlacz
     */
    public function delete($id) {
        $sql = "DELETE FROM client_displays WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Wygeneruj kod parowania dla wyświetlacza
     */
    public function generatePairingCode($displayId) {
        $pairingCode = $this->generateRandomCode();
        $expiresAt = date('Y-m-d H:i:s', time() + 900); // 15 minut

        $sql = "
            UPDATE client_displays 
            SET pairing_code = ?, pairing_status = 'pending', pairing_expires_at = ?
            WHERE id = ?
        ";
        
        $this->db->execute($sql, [$pairingCode, $expiresAt, $displayId]);
        return $pairingCode;
    }

    /**
     * Sparuj wyświetlacz
     */
    public function pair($pairingCode) {
        $display = $this->getByPairingCode($pairingCode);
        
        if (!$display) {
            return false;
        }

        $sql = "
            UPDATE client_displays 
            SET pairing_status = 'paired', paired_at = datetime('now'),
                pairing_code = NULL, pairing_expires_at = NULL
            WHERE id = ?
        ";
        
        return $this->db->execute($sql, [$display['id']]);
    }

    /**
     * Usuń parowanie wyświetlacza
     */
    public function unpair($displayId) {
        $sql = "
            UPDATE client_displays 
            SET pairing_status = 'unpaired', paired_at = NULL,
                pairing_code = NULL, pairing_expires_at = NULL
            WHERE id = ?
        ";
        
        return $this->db->execute($sql, [$displayId]);
    }

    /**
     * Zaktualizuj heartbeat wyświetlacza
     */
    public function updateHeartbeat($displayId) {
        $sql = "
            UPDATE client_displays 
            SET last_heartbeat = datetime('now'), is_online = 1
            WHERE id = ?
        ";
        
        return $this->db->execute($sql, [$displayId]);
    }

    /**
     * Oznacz wyświetlacze jako offline (nie było heartbeat przez 5 minut)
     */
    public function markOfflineDisplays() {
        $sql = "
            UPDATE client_displays 
            SET is_online = 0
            WHERE last_heartbeat < datetime('now', '-5 minutes')
        ";
        
        return $this->db->execute($sql);
    }

    /**
     * Pobierz sparowane wyświetlacze
     */
    public function getPaired() {
        $sql = "
            SELECT * FROM client_displays 
            WHERE pairing_status = 'paired'
            ORDER BY display_name
        ";
        
        return $this->db->fetchAll($sql);
    }

    /**
     * Pobierz wyświetlacze oczekujące na parowanie
     */
    public function getPending() {
        $sql = "
            SELECT * FROM client_displays 
            WHERE pairing_status = 'pending' 
            AND pairing_expires_at > datetime('now')
            ORDER BY pairing_expires_at
        ";
        
        return $this->db->fetchAll($sql);
    }

    /**
     * Wygeneruj unikalny kod wyświetlacza
     */
    private function generateUniqueDisplayCode() {
        do {
            $code = $this->generateRandomCode();
            $existing = $this->getByCode($code);
        } while ($existing);
        
        return $code;
    }

    /**
     * Wygeneruj losowy 6-znakowy kod (małe litery i cyfry)
     */
    private function generateRandomCode() {
        $characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
        $code = '';
        for ($i = 0; $i < 6; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $code;
    }

    /**
     * Wyczyść wygasłe kody parowania
     */
    public function cleanExpiredPairingCodes() {
        $sql = "
            UPDATE client_displays 
            SET pairing_code = NULL, pairing_status = 'unpaired', pairing_expires_at = NULL
            WHERE pairing_status = 'pending' AND pairing_expires_at <= datetime('now')
        ";
        
        return $this->db->execute($sql);
    }
}
