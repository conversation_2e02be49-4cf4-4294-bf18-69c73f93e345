<?php

/**
 * Model Advertiser - zarządzanie reklamodawcami
 */
class Advertiser {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkich reklamodawców
     */
    public function getAllAdvertisers() {
        $sql = "SELECT * FROM ads_advertiser ORDER BY name ASC";
        return $this->db->fetchAll($sql);
    }

    /**
     * Pobierz reklamodawcę po ID
     */
    public function getById($id) {
        $sql = "SELECT * FROM ads_advertiser WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Utwórz nowego reklamodawcę
     */
    public function create($name) {
        $sql = "
            INSERT INTO ads_advertiser (name, created_at)
            VALUES (?, datetime('now'))
        ";

        return $this->db->execute($sql, [$name]);
    }

    /**
     * Zaktualizuj reklamodawcę
     */
    public function update($id, $name) {
        $sql = "UPDATE ads_advertiser SET name = ? WHERE id = ?";
        return $this->db->execute($sql, [$name, $id]);
    }

    /**
     * Usuń reklamodawcę
     */
    public function delete($id) {
        // Sprawdź czy reklamodawca ma przypisane reklamy
        $adsCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM ads WHERE ads_advertiser_id = ?",
            [$id]
        );

        if ($adsCount && $adsCount['count'] > 0) {
            throw new Exception('Nie można usunąć reklamodawcy, który ma przypisane reklamy');
        }

        $sql = "DELETE FROM ads_advertiser WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }
}
