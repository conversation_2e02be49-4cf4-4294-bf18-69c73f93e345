<?php

/**
 * Model VideoCategory - zarządzanie kategoriami video
 */
class VideoCategory {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Pobierz wszystkie kategorie
     */
    public function getAllCategories() {
        $sql = "
            SELECT
                ac.*,
                COUNT(a.id) as video_count,
                SUM(CASE WHEN a.approval_status = 'pending' THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN a.approval_status = 'approved' THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN a.approval_status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
            FROM ads_categories ac
            LEFT JOIN ads a ON ac.id = a.category_id AND a.media_type = 'video'
            GROUP BY ac.id
            ORDER BY ac.name ASC
        ";

        $categories = $this->db->fetchAll($sql);

        // Dodaj informacje o auto-akceptacji z settings
        foreach ($categories as &$category) {
            $settingKey = "video_category_auto_accept_{$category['id']}";
            $autoAccept = $this->db->fetchOne(
                "SELECT value FROM settings WHERE key = ?",
                [$settingKey]
            );
            $category['auto_accept'] = $autoAccept ? (int)$autoAccept['value'] : 0;
        }

        return $categories;
    }

    /**
     * Pobierz kategorię po ID
     */
    public function getById($id) {
        $sql = "SELECT * FROM ads_categories WHERE id = ?";
        $category = $this->db->fetchOne($sql, [$id]);

        if ($category) {
            // Dodaj informacje o auto-akceptacji z settings
            $settingKey = "video_category_auto_accept_{$category['id']}";
            $autoAccept = $this->db->fetchOne(
                "SELECT value FROM settings WHERE key = ?",
                [$settingKey]
            );
            $category['auto_accept'] = $autoAccept ? (int)$autoAccept['value'] : 0;
        }

        return $category;
    }

    /**
     * Zaktualizuj kategorię
     */
    public function update($id, $name, $description, $autoAccept) {
        // Aktualizuj podstawowe dane kategorii
        $sql = "
            UPDATE ads_categories
            SET name = ?, description = ?
            WHERE id = ?
        ";
        $result = $this->db->execute($sql, [$name, $description, $id]);

        // Zapisz ustawienie auto-akceptacji w settings
        $settingKey = "video_category_auto_accept_{$id}";
        if ($autoAccept) {
            $this->db->execute(
                "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime('now'))",
                [$settingKey, '1']
            );
        } else {
            $this->db->execute(
                "DELETE FROM settings WHERE key = ?",
                [$settingKey]
            );
        }

        return $result;
    }

    /**
     * Utwórz nową kategorię
     */
    public function create($name, $description = '', $autoAccept = 0) {
        $sql = "
            INSERT INTO ads_categories (name, description, created_at)
            VALUES (?, ?, datetime('now'))
        ";

        $result = $this->db->execute($sql, [$name, $description]);

        if ($result && $autoAccept) {
            $categoryId = $this->db->lastInsertId();
            $settingKey = "video_category_auto_accept_{$categoryId}";
            $this->db->execute(
                "INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, datetime('now'), datetime('now'))",
                [$settingKey, '1']
            );
        }

        return $result;
    }

    /**
     * Usuń kategorię
     */
    public function delete($id) {
        // Sprawdź czy kategoria ma przypisane materiały video
        $videoCount = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM ads WHERE category_id = ? AND media_type = 'video'",
            [$id]
        );

        if ($videoCount && $videoCount['count'] > 0) {
            throw new Exception('Nie można usunąć kategorii, która ma przypisane materiały video');
        }

        // Usuń ustawienia auto-akceptacji
        $settingKey = "video_category_auto_accept_{$id}";
        $this->db->execute("DELETE FROM settings WHERE key = ?", [$settingKey]);

        $sql = "DELETE FROM ads_categories WHERE id = ?";
        return $this->db->execute($sql, [$id]);
    }

    /**
     * Przełącz automatyczną akceptację
     */
    public function toggleAutoAccept($id) {
        $settingKey = "video_category_auto_accept_{$id}";

        // Sprawdź obecny stan
        $current = $this->db->fetchOne(
            "SELECT value FROM settings WHERE key = ?",
            [$settingKey]
        );

        if ($current && $current['value'] == '1') {
            // Wyłącz auto-akceptację
            $this->db->execute(
                "DELETE FROM settings WHERE key = ?",
                [$settingKey]
            );
        } else {
            // Włącz auto-akceptację
            $this->db->execute(
                "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime('now'))",
                [$settingKey, '1']
            );
        }

        return true;
    }

    /**
     * Pobierz kategorie z automatyczną akceptacją
     */
    public function getAutoAcceptCategories() {
        $sql = "SELECT * FROM video_categories WHERE auto_accept = 1 ORDER BY name";
        return $this->db->fetchAll($sql);
    }

    /**
     * Sprawdź czy nazwa kategorii już istnieje
     */
    public function nameExists($name, $excludeId = null) {
        $sql = "SELECT id FROM video_categories WHERE name = ?";
        $params = [$name];

        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }

        $result = $this->db->fetchOne($sql, $params);
        return $result !== false;
    }

    /**
     * Pobierz statystyki kategorii
     */
    public function getStats() {
        $sql = "
            SELECT 
                COUNT(*) as total_categories,
                SUM(auto_accept) as auto_accept_categories,
                (SELECT COUNT(*) FROM ads WHERE media_type = 'video' AND video_category_id IS NOT NULL) as categorized_videos,
                (SELECT COUNT(*) FROM ads WHERE media_type = 'video' AND video_category_id IS NULL) as uncategorized_videos
            FROM video_categories
        ";

        return $this->db->fetchOne($sql);
    }
}
