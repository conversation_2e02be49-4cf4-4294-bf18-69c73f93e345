# 🧪 Testy KtoOstatni.pl v4 - Instrukcja Uruchamiania

## 📋 Przegląd

Kompleksowy system testów dla aplikacji KtoOstatni.pl v4 wykorzystujący **Pest PHP** do testowania:
- **API v2** - wszystkie endpointy i kontrolery
- **Panel Admina** - wszystkie funkcjonalności administracyjne  
- **Modele** - logika biznesowa i operacje bazodanowe
- **Integracja** - komunikacja między komponentami

## 🚀 Szybki Start

### 1. Instalacja <PERSON>

```bash
# Zainstaluj Composer (jeśli nie masz)
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# Zainstaluj zależności testowe
composer install
```

### 2. Uruchomienie Wszystkich Testów

```bash
# Wszystkie testy
composer test

# Lub bezpośrednio przez Pest
./vendor/bin/pest
```

### 3. Uruchomienie Konkretnych Grup Testów

```bash
# Tylko testy jednostkowe
composer test-unit

# Tylko testy funkcjonalne
composer test-feature

# Tylko testy integracyjne
composer test-integration
```

## 📁 Struktura Testów

```
tests/
├── Unit/                    # Testy jednostkowe
│   ├── Models/             # Testy modeli (Doctor, Appointment, Database)
│   └── Controllers/        # Testy kontrolerów
│       ├── Api/           # Kontrolery API v2
│       └── Admin/         # Kontrolery panelu admina
├── Feature/                # Testy funkcjonalne (będą dodane)
├── Integration/            # Testy integracyjne (będą dodane)
├── Fixtures/              # Dane testowe
├── database/              # Testowa baza danych
├── coverage/              # Raporty pokrycia kodu
├── bootstrap.php          # Inicjalizacja testów
├── Pest.php              # Konfiguracja Pest
├── TestCase.php          # Bazowa klasa testowa
└── UnitTestCase.php      # Klasa dla testów jednostkowych
```

## 🎯 Dostępne Komendy

### Podstawowe Uruchamianie

```bash
# Wszystkie testy z kolorowym wyjściem
./vendor/bin/pest

# Testy z szczegółowym wyjściem
./vendor/bin/pest --verbose

# Zatrzymaj na pierwszym błędzie
./vendor/bin/pest --stop-on-failure

# Uruchom tylko konkretny test
./vendor/bin/pest tests/Unit/Models/DoctorTest.php

# Uruchom testy pasujące do wzorca
./vendor/bin/pest --filter="login"
```

### Raporty Pokrycia Kodu

```bash
# Raport pokrycia w HTML
composer test-coverage

# Raport pokrycia w terminalu
./vendor/bin/pest --coverage

# Raport pokrycia z minimalnym progiem
./vendor/bin/pest --coverage --min=80
```

### Debugowanie

```bash
# Tryb debug z dodatkowymi informacjami
./vendor/bin/pest --debug

# Uruchom konkretny test z debugiem
./vendor/bin/pest tests/Unit/Models/DoctorTest.php --debug
```

## 📊 Interpretacja Wyników

### ✅ Sukces
```
✓ can login doctor with valid access code
✓ returns appointments for valid doctor  
✓ calculates delay correctly

Tests:  25 passed
Time:   1.23s
```

### ❌ Błędy
```
✗ fails with invalid access code
  Expected false, but got true

Tests:  24 passed, 1 failed
Time:   1.45s
```

### 📈 Pokrycie Kodu
```
Coverage Report:
  admin/controllers/: 95.2%
  admin/models/: 98.7%
  api/v2/controllers/: 92.1%
  
Total Coverage: 94.3%
```

## 🔧 Konfiguracja

### Zmienne Środowiskowe

Testy używają następujących ustawień (w `phpunit.xml`):

```xml
<env name="APP_ENV" value="testing"/>
<env name="DB_PATH" value="tests/database/test.db"/>
<env name="TESTING" value="true"/>
```

### Testowa Baza Danych

- **Lokalizacja**: `tests/database/test.db`
- **Typ**: SQLite (izolowana od produkcyjnej)
- **Reset**: Automatyczny przed każdym testem
- **Fixtures**: Automatyczne ładowanie danych testowych

## 📝 Pisanie Nowych Testów

### Test Jednostkowy Modelu

```php
<?php

use Tests\Fixtures\DatabaseFixtures;

beforeEach(function () {
    $this->fixtures = new DatabaseFixtures($this->createTestPdo());
    $this->fixtures->loadAll();
    $this->model = new YourModel();
});

describe('YourModel', function () {
    it('performs basic operation', function () {
        $result = $this->model->someMethod();
        
        expect($result)->toBeArray()
            ->and($result)->toHaveCount(3);
    });
});
```

### Test Kontrolera API

```php
<?php

beforeEach(function () {
    $this->controller = new YourApiController();
});

describe('YourApiController', function () {
    it('returns valid API response', function () {
        $result = $this->controller->someEndpoint();
        
        expect($result)->toBeValidApiResponse()
            ->and($result['data'])->toHaveKey('expected_field');
    });
});
```

## 🎨 Dostępne Expectation

### Podstawowe
```php
expect($value)->toBe(123);
expect($array)->toHaveCount(5);
expect($string)->toContain('text');
expect($number)->toBeGreaterThan(10);
```

### Niestandardowe (zdefiniowane w `tests/Pest.php`)
```php
expect($response)->toBeValidApiResponse();
expect($response)->toBeValidErrorResponse();
expect($doctor)->toHaveValidDoctorStructure();
expect($appointment)->toHaveValidAppointmentStructure();
```

## 🚨 Rozwiązywanie Problemów

### Problem: Baza danych nie istnieje
```bash
# Usuń i pozwól na ponowne utworzenie
rm tests/database/test.db
./vendor/bin/pest
```

### Problem: Błędy uprawnień
```bash
# Ustaw uprawnienia do katalogu testów
chmod -R 755 tests/
chmod -R 777 tests/database/
```

### Problem: Brakujące zależności
```bash
# Reinstaluj zależności
rm -rf vendor/
composer install
```

### Problem: Błędy autoloadera
```bash
# Regeneruj autoloader
composer dump-autoload
```

## 📈 Metryki i Cele

### Cele Pokrycia Kodu
- **Modele**: 95%+
- **Kontrolery API**: 90%+  
- **Kontrolery Admin**: 90%+
- **Ogólne**: 90%+

### Cele Wydajności
- **Test jednostkowy**: < 100ms
- **Test integracyjny**: < 1s
- **Cały zestaw**: < 30s

## 🔄 Integracja z CI/CD

### GitHub Actions (przykład)
```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: composer test-coverage
```

## 📞 Wsparcie

W przypadku problemów:

1. **Sprawdź logi**: `tests/debug.log`
2. **Uruchom z debugiem**: `./vendor/bin/pest --debug`
3. **Sprawdź dokumentację Pest**: https://pestphp.com/docs
4. **Zresetuj środowisko testowe**: `rm -rf tests/database/ && composer test`

---

**Powodzenia z testowaniem! 🎉**
