<?php

/**
 * Migracja: Dodanie tabeli kategorii video z automatyczną akceptacją
 */

define('KTOOSTATNI_APP', true);
require_once __DIR__ . '/config.php';

echo "=== Migracja: Dodanie tabeli kategorii video ===\n\n";

try {
    $db = Database::getInstance();
    
    // Sprawdź czy tabela już istnieje
    $tableExists = $db->fetchOne("
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='video_categories'
    ");
    
    if ($tableExists) {
        echo "✅ Tabela video_categories już istnieje.\n";
    } else {
        // Utwórz tabelę kategorii video
        $sql = "
            CREATE TABLE video_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                auto_accept INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ";
        
        $db->execute($sql);
        echo "✅ Utworzono tabelę video_categories.\n";
        
        // Dodaj przykładowe kategorie
        $categories = [
            ['name' => 'Edukacja medyczna', 'description' => 'Materiały edukacyjne o zdrowiu', 'auto_accept' => 1],
            ['name' => 'Promocje kliniki', 'description' => 'Materiały promocyjne placówki', 'auto_accept' => 1],
            ['name' => 'Reklamy zewnętrzne', 'description' => 'Materiały reklamowe firm zewnętrznych', 'auto_accept' => 0],
            ['name' => 'Informacje publiczne', 'description' => 'Ogłoszenia i informacje publiczne', 'auto_accept' => 1],
        ];
        
        foreach ($categories as $category) {
            $db->execute(
                "INSERT INTO video_categories (name, description, auto_accept) VALUES (?, ?, ?)",
                [$category['name'], $category['description'], $category['auto_accept']]
            );
        }
        
        echo "✅ Dodano przykładowe kategorie video.\n";
    }
    
    // Sprawdź czy kolumna video_category_id istnieje w tabeli ads
    $columnExists = $db->fetchOne("
        PRAGMA table_info(ads)
    ");
    
    $hasVideoCategory = false;
    $columns = $db->fetchAll("PRAGMA table_info(ads)");
    foreach ($columns as $column) {
        if ($column['name'] === 'video_category_id') {
            $hasVideoCategory = true;
            break;
        }
    }
    
    if (!$hasVideoCategory) {
        // Dodaj kolumnę video_category_id do tabeli ads
        $db->execute("ALTER TABLE ads ADD COLUMN video_category_id INTEGER");
        $db->execute("ALTER TABLE ads ADD COLUMN approval_status VARCHAR(20) DEFAULT 'pending'");
        echo "✅ Dodano kolumny video_category_id i approval_status do tabeli ads.\n";
    } else {
        echo "✅ Kolumny video_category_id i approval_status już istnieją w tabeli ads.\n";
    }
    
    echo "\n=== Migracja zakończona pomyślnie! ===\n";
    
} catch (Exception $e) {
    echo "❌ Błąd podczas migracji: " . $e->getMessage() . "\n";
    exit(1);
}
