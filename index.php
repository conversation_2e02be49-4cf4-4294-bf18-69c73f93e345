<?php

/**
 * Router główny aplikacji KtoOstatni.pl v4
 * Obsługuje wszystkie requesty do aplikacji
 */

define('KTOOSTATNI_APP', true);
require_once __DIR__ . '/config.php';

// Obsługa plików statycznych dla PHP dev server
if (php_sapi_name() === 'cli-server') {
    $requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $requestedFile = __DIR__ . $requestUri;

    // Jeśli plik istnieje i nie jest index.php, pozwól serwerowi go obsłużyć
    if (is_file($requestedFile) && basename($requestedFile) !== 'index.php') {
        return false; // Pozwól serwerowi obsłużyć plik statyczny
    }
}

// Pobierz ścieżkę z URL
$requestUri = $_SERVER['REQUEST_URI'];

// Usuń parametry GET z URL
$path = parse_url($requestUri, PHP_URL_PATH);

// Usuń początkowy i końcowy slash
$path = trim($path, '/');

// Dla PHP development server - sprawdź czy to bezpośrednie wywołanie index.php
if (php_sapi_name() === 'cli-server' && $path === 'index.php') {
    // Przekieruj na admin jeśli wywołano bezpośrednio index.php
    redirect('/admin');
}

// Jeśli ścieżka jest pusta, przekieruj na admin
if (empty($path)) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Index: Przekierowanie z pustej ścieżki na /admin");
    }
    redirect('/admin');
}

// Obsługa API v2
if (strpos($path, 'api/v2') === 0) {
    require_once __DIR__ . '/api/v2/index.php';
    exit;
}

// Obsługa aplikacji PWA dla lekarzy
if (strpos($path, 'lekarz') === 0) {
    // Sprawdź czy to żądanie pliku statycznego w folderze lekarz
    $requestedFile = __DIR__ . '/' . $path;
    if (is_file($requestedFile)) {
        return false; // Pozwól serwerowi obsłużyć plik statyczny
    }

    // Dla wszystkich innych żądań w /lekarz przekieruj na index.html
    require_once __DIR__ . '/lekarz/index.html';
    exit;
}

// Obsługa wyświetlaczy publicznych
if (strpos($path, 'display/') === 0 || $path === 'display') {
    require_once __DIR__ . '/display/index.php';
    exit;
}

// Routing
$routes = [
    // Autoryzacja
    'admin/login' => ['AuthController', 'login'],
    'admin/logout' => ['AuthController', 'logout'],

    // Panel główny (pusty na razie)
    'admin' => ['DashboardController', 'index'],
    'admin/pulpit' => ['DashboardController', 'index'],

    // Zarządzanie kolejkami (przeniesiona zawartość z dashboard)
    'admin/queue' => ['QueueController', 'index'],
    'admin/kolejki' => ['QueueController', 'index'],
    'admin/queue/call-next' => ['QueueController', 'callNext'],
    'admin/queue/call-patient' => ['QueueController', 'callPatient'],
    'admin/queue/complete-appointment' => ['QueueController', 'completeAppointment'],
    'admin/kolejki/call-next' => ['QueueController', 'callNext'],
    'admin/kolejki/call-patient' => ['QueueController', 'callPatient'],
    'admin/kolejki/complete-appointment' => ['QueueController', 'completeAppointment'],

    // Zarządzanie lekarzami
    'admin/doctors' => ['DoctorController', 'index'],
    'admin/lekarze' => ['DoctorController', 'index'],
    'admin/doctors/create' => ['DoctorController', 'create'],
    'admin/lekarze/create' => ['DoctorController', 'create'],
    'admin/doctors/store' => ['DoctorController', 'store'],
    'admin/lekarze/store' => ['DoctorController', 'store'],

    // Zarządzanie wyświetlaczami
    'admin/displays' => ['DisplayController', 'index'],
    'admin/wyswietlacze' => ['DisplayController', 'index'],
    'admin/displays/create' => ['DisplayController', 'create'],
    'admin/wyswietlacze/create' => ['DisplayController', 'create'],
    'admin/displays/store' => ['DisplayController', 'store'],
    'admin/wyswietlacze/store' => ['DisplayController', 'store'],
    'admin/displays/pair' => ['DisplayController', 'pair'],
    'admin/wyswietlacze/pair' => ['DisplayController', 'pair'],
    'admin/displays/update-name' => ['DisplayController', 'updateName'],
    'admin/wyswietlacze/update-name' => ['DisplayController', 'updateName'],
    'admin/displays/unpair' => ['DisplayController', 'unpair'],
    'admin/wyswietlacze/unpair' => ['DisplayController', 'unpair'],
    'admin/displays/delete' => ['DisplayController', 'delete'],
    'admin/wyswietlacze/delete' => ['DisplayController', 'delete'],
    'admin/doctors/edit' => ['DoctorController', 'edit'],
    'admin/lekarze/edit' => ['DoctorController', 'edit'],
    'admin/doctors/update' => ['DoctorController', 'update'],
    'admin/lekarze/update' => ['DoctorController', 'update'],
    'admin/doctors/delete' => ['DoctorController', 'delete'],
    'admin/lekarze/delete' => ['DoctorController', 'delete'],
    'admin/doctors/regenerate-code' => ['DoctorController', 'regenerateCode'],
    'admin/lekarze/regenerate-code' => ['DoctorController', 'regenerateCode'],

    // Video management
    'admin/video' => ['VideoController', 'index'],
    'admin/video/all' => ['VideoController', 'allVideos'],
    'admin/video/category' => ['VideoController', 'categoryVideos'],
    'admin/video/edit-category' => ['VideoController', 'editCategory'],
    'admin/video/update-category' => ['VideoController', 'updateCategory'],
    'admin/video/approve-category' => ['VideoController', 'approveCategoryVideos'],
    'admin/video/approve' => ['VideoController', 'approve'],
    'admin/video/reject' => ['VideoController', 'reject'],
    'admin/video/toggle-auto-accept' => ['VideoController', 'toggleAutoAccept'],
    'admin/video/add-category' => ['VideoController', 'addCategory'],
    'admin/video/delete-category' => ['VideoController', 'deleteCategory'],
    'admin/video/get-video-data' => ['VideoController', 'getVideoData'],
    'admin/video/get-advertisers' => ['VideoController', 'getAdvertisers'],
    'admin/video/update' => ['VideoController', 'updateVideo'],
    'admin/video/add' => ['VideoController', 'addVideo'],
    'admin/video/store' => ['VideoController', 'storeVideo'],

    // Zarządzanie wizytami
    'admin/appointments/create' => ['AppointmentController', 'create'],
    'admin/appointments/store' => ['AppointmentController', 'store'],
    'admin/appointments/edit' => ['AppointmentController', 'edit'],
    'admin/appointments/update' => ['AppointmentController', 'update'],
    'admin/appointments/delete' => ['AppointmentController', 'delete'],

    // Ustawienia
    'admin/settings' => ['SettingsController', 'index'],

    // API endpoints
    'admin/api/queue-status' => ['QueueController', 'getQueueStatus'],
    'admin/api/doctor-stats' => ['DoctorController', 'getStats'],
];

// Sprawdź czy route istnieje
if (!isset($routes[$path])) {
    // Sprawdź czy to route z parametrem (np. admin/doctors/edit/1)
    $pathParts = explode('/', $path);
    $routeFound = false;

    if (count($pathParts) >= 4) {
        // Routes z ID na końcu
        $baseRoute = implode('/', array_slice($pathParts, 0, 3));
        $id = $pathParts[3];

        $paramRoutes = [
            'admin/doctors/edit' => ['DoctorController', 'edit'],
            'admin/lekarze/edit' => ['DoctorController', 'edit'],
            'admin/doctors/update' => ['DoctorController', 'update'],
            'admin/lekarze/update' => ['DoctorController', 'update'],
            'admin/doctors/delete' => ['DoctorController', 'delete'],
            'admin/lekarze/delete' => ['DoctorController', 'delete'],
            'admin/doctors/regenerate-code' => ['DoctorController', 'regenerateCode'],
            'admin/lekarze/regenerate-code' => ['DoctorController', 'regenerateCode'],

            // Video management
            'admin/video' => ['VideoController', 'index'],
            'admin/video/all' => ['VideoController', 'allVideos'],
            'admin/video/category' => ['VideoController', 'categoryVideos'],
            'admin/video/edit-category' => ['VideoController', 'editCategory'],
            'admin/video/update-category' => ['VideoController', 'updateCategory'],
            'admin/video/approve-category' => ['VideoController', 'approveCategoryVideos'],
            'admin/video/approve' => ['VideoController', 'approve'],
            'admin/video/reject' => ['VideoController', 'reject'],
            'admin/video/toggle-auto-accept' => ['VideoController', 'toggleAutoAccept'],
            'admin/video/add-category' => ['VideoController', 'addCategory'],
            'admin/video/delete-category' => ['VideoController', 'deleteCategory'],
            'admin/video/get-video-data' => ['VideoController', 'getVideoData'],
            'admin/video/get-advertisers' => ['VideoController', 'getAdvertisers'],
            'admin/video/update' => ['VideoController', 'updateVideo'],
            'admin/video/add' => ['VideoController', 'addVideo'],
            'admin/video/store' => ['VideoController', 'storeVideo'],
            'admin/appointments/edit' => ['AppointmentController', 'edit'],
            'admin/appointments/update' => ['AppointmentController', 'update'],
            'admin/appointments/delete' => ['AppointmentController', 'delete'],
            'admin/appointments/details' => ['AppointmentController', 'details'],
        ];

        if (isset($paramRoutes[$baseRoute])) {
            $routes[$path] = $paramRoutes[$baseRoute];
            $_GET['id'] = $id; // Dodaj ID do $_GET
            $routeFound = true;
        }
    }

    if (!$routeFound) {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Index: Nie znaleziono routingu dla ścieżki: '$path'");
            error_log("Index: Dostępne routes: " . implode(', ', array_keys($routes)));
        }
        http_response_code(404);
        die('Strona nie została znaleziona');
    }
}

// Pobierz controller i metodę
list($controllerName, $method) = $routes[$path];

// Sprawdź autoryzację (pomiń dla strony logowania)
if ($path !== 'admin/login' && !isLoggedIn()) {
    redirect('/admin/login');
}

// Sprawdź czy controller istnieje
if (!class_exists($controllerName)) {
    http_response_code(500);
    die('Controller nie został znaleziony: ' . $controllerName);
}

// Utwórz instancję controllera
$controller = new $controllerName();

// Sprawdź czy metoda istnieje
if (!method_exists($controller, $method)) {
    http_response_code(500);
    die('Metoda nie została znaleziona: ' . $method);
}

// Wywołaj metodę
try {
    $controller->$method();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die('Błąd aplikacji: ' . $e->getMessage() . '<br>Plik: ' . $e->getFile() . '<br>Linia: ' . $e->getLine());
    } else {
        error_log('Błąd aplikacji: ' . $e->getMessage());
        http_response_code(500);
        die('Wystąpił błąd serwera');
    }
}
