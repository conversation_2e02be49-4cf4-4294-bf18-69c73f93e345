<?php

/**
 * Migracja: Dodanie kodów dostępu dla istniejących lekarzy
 * Generuje unikalne kody dostępu dla lekarzy, którzy ich nie mają
 */

define('KTOOSTATNI_APP', true);
require_once __DIR__ . '/config.php';

echo "=== Migracja: Dodanie kodów dostępu dla lekarzy ===\n\n";

try {
    $db = Database::getInstance();
    
    // Sprawdź lekarzy bez kodów dostępu
    $doctorsWithoutCodes = $db->fetchAll("
        SELECT id, first_name, last_name 
        FROM queue_doctors 
        WHERE access_code IS NULL OR access_code = ''
    ");
    
    if (empty($doctorsWithoutCodes)) {
        echo "✅ Wszyscy lekarze mają już kody dostępu.\n";
        exit(0);
    }
    
    echo "Znaleziono " . count($doctorsWithoutCodes) . " lekarzy bez kodów dostępu:\n";
    foreach ($doctorsWithoutCodes as $doctor) {
        echo "- ID: {$doctor['id']}, {$doctor['first_name']} {$doctor['last_name']}\n";
    }
    echo "\n";
    
    // Funkcja do generowania unikalnego kodu dostępu
    function generateAccessCode($db) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyz';
        
        do {
            $code = '';
            // Generuj 12 znaków
            for ($i = 0; $i < 12; $i++) {
                $code .= $characters[random_int(0, strlen($characters) - 1)];
            }

            // Sprawdź czy kod już istnieje
            $existing = $db->fetchOne(
                "SELECT id FROM queue_doctors WHERE access_code = ?",
                [$code]
            );
        } while ($existing);

        return $code;
    }
    
    // Generuj kody dla każdego lekarza
    $updated = 0;
    foreach ($doctorsWithoutCodes as $doctor) {
        $accessCode = generateAccessCode($db);
        
        $result = $db->execute(
            "UPDATE queue_doctors SET access_code = ?, updated_at = datetime('now') WHERE id = ?",
            [$accessCode, $doctor['id']]
        );
        
        if ($result) {
            echo "✅ Wygenerowano kod dla {$doctor['first_name']} {$doctor['last_name']}: {$accessCode}\n";
            $updated++;
        } else {
            echo "❌ Błąd podczas generowania kodu dla {$doctor['first_name']} {$doctor['last_name']}\n";
        }
    }
    
    echo "\n=== Podsumowanie ===\n";
    echo "Zaktualizowano: {$updated} lekarzy\n";
    echo "Migracja zakończona pomyślnie!\n";
    
} catch (Exception $e) {
    echo "❌ Błąd podczas migracji: " . $e->getMessage() . "\n";
    exit(1);
}
