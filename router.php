<?php

// Router dla PHP Development Server
// Obsługuje pliki statyczne i przekierowuje resztę na index.php

file_put_contents('php://stderr', "Router: " . $_SERVER['REQUEST_URI'] . "\n");

$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$requestedFile = __DIR__ . $requestUri;

// Jeśli plik istnieje i nie jest plikiem PHP, pozwól serwerowi go obsłużyć
if (is_file($requestedFile) && pathinfo($requestedFile, PATHINFO_EXTENSION) !== 'php') {
    return false; // Pozwól serwerowi obsłużyć plik statyczny
}

// Wszystkie inne żądania (w tym nieistniejące pliki) przekieruj na index.php
require_once __DIR__ . '/index.php';
