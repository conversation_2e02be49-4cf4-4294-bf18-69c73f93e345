# Specyfikacja Bazy Danych - System KtoOstatni.pl

## Przegląd Systemu

System KtoOstatni.pl to aplikacja do zarządzania kolejkami w placówkach medycznych z integracją systemów reklamowych. Baza danych wykorzystuje SQLite i zawiera tabele do zarządzania użytkownikami, kampaniami reklamowymi, systemem kolejkowym oraz importem danych z zewnętrznych systemów.

---

## Tabele Główne

### 1. `users` - Użytkownicy Systemu

**Opis:** Tabela przechowuje informacje o wszystkich użytkownikach systemu (administratorzy, klienci, reklamodawcy).

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator użytkownika | Auto-increment |
| `username` | VARCHAR(50) UNIQUE | Nazwa użytkownika (login) | Unikalna, wymagana |
| `email` | VARCHAR(100) UNIQUE | Adres email użytkownika | Unikalny, wymagany |
| `password` | VARCHAR(255) | Hash hasła użytkownika | Wymagany, hashowany |
| `role` | TEXT | Rola użytkownika w systemie | **Wartości:** `admin`, `client`, `advertiser` |
| `company_name` | VARCHAR(100) | Nazwa firmy/placówki | Opcjonalne |
| `balance` | DECIMAL(10,2) | Saldo konta użytkownika | Domyślnie: 0.00 |
| `rate_per_second` | DECIMAL(5,4) | Stawka za sekundę wyświetlania | Domyślnie: 0.0001 |
| `is_active` | INTEGER | Status aktywności konta | **Wartości:** `0` (nieaktywne), `1` (aktywne) |
| `last_activity` | DATETIME | Data ostatniej aktywności | Opcjonalne |
| `created_at` | DATETIME | Data utworzenia konta | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- UNIQUE na `username`
- UNIQUE na `email`

---

### 2. `categories` - Kategorie Kampanii

**Opis:** Tabela kategorii dla kampanii reklamowych.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator kategorii | Auto-increment |
| `name` | VARCHAR(100) UNIQUE | Nazwa kategorii | Wymagana, unikalna |
| `description` | TEXT | Opis kategorii | Opcjonalne |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Przykładowe kategorie:**
- Edukacja
- Medycyna
- Firmy lokalne
- Finanse
- Rozrywka
- Sport
- Żywność
- Technologia
- Moda
- Motoryzacja

---

### 3. `campaigns` - Kampanie Reklamowe

**Opis:** Tabela kampanii reklamowych tworzonych przez reklamodawców.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator kampanii | Auto-increment |
| `advertiser_id` | INTEGER | ID reklamodawcy | FK do `users(id)`, wymagane |
| `category_id` | INTEGER | ID kategorii kampanii | FK do `categories(id)`, opcjonalne |
| `name` | VARCHAR(100) | Nazwa kampanii | Wymagana |
| `description` | TEXT | Opis kampanii | Opcjonalne |
| `media_type` | TEXT | Typ mediów | **Wartości:** `image`, `video`, `youtube` |
| `media_url` | VARCHAR(255) | URL do pliku multimedialnego | Wymagany |
| `youtube_id` | VARCHAR(20) | ID filmu YouTube | Opcjonalne, tylko dla `media_type = 'youtube'` |
| `duration` | INTEGER | Czas trwania w sekundach | Domyślnie: 30 |
| `budget` | DECIMAL(10,2) | Budżet kampanii | Wymagany |
| `spent` | DECIMAL(10,2) | Wydane środki | Domyślnie: 0.00 |
| `rate_per_second` | DECIMAL(5,4) | Stawka za sekundę | Domyślnie: 0.0001 |
| `max_frequency_per_hour` | INTEGER | Maks. wyświetleń na godzinę | Domyślnie: 0 (bez limitu) |
| `start_date` | DATETIME | Data rozpoczęcia kampanii | Opcjonalne |
| `end_date` | DATETIME | Data zakończenia kampanii | Opcjonalne |
| `is_active` | INTEGER | Status kampanii | **Wartości:** `0` (nieaktywna), `1` (aktywna) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `advertiser_id` → `users(id)`
- FK na `category_id` → `categories(id)`

---

### 4. `ad_views` - Wyświetlenia Reklam

**Opis:** Tabela logów wyświetleń reklam z informacjami o kosztach.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator wyświetlenia | Auto-increment |
| `campaign_id` | INTEGER | ID kampanii | FK do `campaigns(id)`, wymagane |
| `client_id` | INTEGER | ID klienta (gdzie wyświetlono) | FK do `users(id)`, wymagane |
| `duration_seconds` | INTEGER | Czas wyświetlania w sekundach | Wymagany |
| `cost` | DECIMAL(10,4) | Koszt wyświetlenia | Wymagany |
| `timestamp` | DATETIME | Data i czas wyświetlenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `campaign_id` → `campaigns(id)`
- FK na `client_id` → `users(id)`

---

### 5. `client_displays` - Wyświetlacze Klientów

**Opis:** Tabela monitorów/TV należących do klientów.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator wyświetlacza | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `display_name` | VARCHAR(100) | Nazwa wyświetlacza | Wymagana |
| `display_code` | VARCHAR(6) UNIQUE | 6-znakowy kod wyświetlacza | Unikalny, opcjonalny |
| `is_online` | INTEGER | Status połączenia | **Wartości:** `0` (offline), `1` (online) |
| `last_heartbeat` | DATETIME | Ostatni sygnał żywotności | Opcjonalne |
| `queue_system_enabled` | INTEGER | Czy system kolejkowy włączony | **Wartości:** `0` (wyłączony), `1` (włączony) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- UNIQUE na `display_code`

---

## Tabele Systemu Kolejkowego

### 6. `queue_config` - Konfiguracja Systemu Kolejkowego

**Opis:** Konfiguracja systemu kolejkowego dla każdego klienta.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator konfiguracji | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `is_enabled` | INTEGER | Czy system kolejkowy włączony | **Wartości:** `0` (wyłączony), `1` (włączony) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`

---

### 7. `queue_doctors` - Lekarze w Systemie Kolejkowym

**Opis:** Tabela lekarzy przypisanych do klientów.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator lekarza | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `first_name` | VARCHAR(100) | Imię lekarza | Wymagane |
| `last_name` | VARCHAR(100) | Nazwisko lekarza | Wymagane |
| `photo_url` | VARCHAR(255) | URL do zdjęcia lekarza | Opcjonalne |
| `specialization` | VARCHAR(200) | Specjalizacja lekarza | Opcjonalne |
| `access_code` | VARCHAR(12) UNIQUE | 12-znakowy kod dostępu | Unikalny, opcjonalny |
| `active` | INTEGER | Status aktywności | **Wartości:** `0` (nieaktywny), `1` (aktywny) |
| `default_room_id` | INTEGER | ID domyślnego gabinetu | FK do `queue_rooms(id)`, opcjonalne |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `default_room_id` → `queue_rooms(id)`
- UNIQUE na `access_code`

---

### 8. `queue_rooms` - Sale/Gabinety

**Opis:** Tabela sal/gabinetów w placówkach medycznych.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator sali | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `name` | VARCHAR(100) | Nazwa sali/gabinetu | Wymagana |
| `description` | TEXT | Opis sali | Opcjonalne |
| `doctor_id` | INTEGER | ID przypisanego lekarza | FK do `queue_doctors(id)`, opcjonalne |
| `room_number` | VARCHAR(20) | Numer sali | Opcjonalne |
| `active` | INTEGER | Status aktywności | **Wartości:** `0` (nieaktywna), `1` (aktywna) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `doctor_id` → `queue_doctors(id)`

---

### 9. `queue_appointments` - Wizyty w Systemie Kolejkowym

**Opis:** Tabela wizyt/umówionych spotkań w systemie kolejkowym.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator wizyty | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `doctor_id` | INTEGER | ID lekarza | FK do `queue_doctors(id)`, opcjonalne |
| `appointment_time` | TIME | Godzina wizyty | Wymagana |
| `appointment_duration` | INTEGER | Czas trwania wizyty (minuty) | Domyślnie: 20 |
| `appointment_date` | DATE | Data wizyty | Opcjonalne |
| `patient_name` | VARCHAR(200) | Imię i nazwisko pacjenta | Opcjonalne |
| `status` | TEXT | Status wizyty | **Wartości:** `waiting` (oczekuje), `current` (aktualna), `closed` (zakończona) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |
| `started_at` | DATETIME | Czas rozpoczęcia wizyty | Opcjonalne |
| `called_at` | DATETIME | Czas wywołania pacjenta | Opcjonalne |
| `completed_at` | DATETIME | Czas zakończenia wizyty | Opcjonalne |
| `external_id` | VARCHAR(100) | ID z systemu zewnętrznego | Opcjonalne |
| `type` | VARCHAR(50) | Typ wizyty | Domyślnie: 'wizyta' |
| `phone_number` | VARCHAR(20) | Numer telefonu pacjenta | Opcjonalne |
| `is_confirmed` | INTEGER | Czy wizyta potwierdzona | **Wartości:** `0` (niepotwierdzona), `1` (potwierdzona) |
| `is_patient_present` | INTEGER | Czy pacjent obecny | **Wartości:** `0` (nieobecny), `1` (obecny) |
| `tracking_code` | VARCHAR(16) | 16-znakowy kod śledzenia | Unikalny, opcjonalny |
| `sms_sent` | INTEGER | Czy wysłano SMS | **Wartości:** `0` (nie wysłano), `1` (wysłano) |
| `locked` | INTEGER | Czy wizyta zablokowana | **Wartości:** `0` (odblokowana), `1` (zablokowana) |
| `visit` | INTEGER | Flaga wizyty | **Wartości:** `0`, `1` |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `doctor_id` → `queue_doctors(id)`
- UNIQUE na `tracking_code`
- UNIQUE na `external_id, client_id`
- INDEX na `doctor_id, appointment_date`
- INDEX na `client_id, appointment_date`

**Statusy wizyt:**
- `waiting` - Pacjent oczekuje w kolejce
- `current` - Pacjent jest obecnie obsługiwany
- `closed` - Wizyta zakończona

---

### 10. `queue_numbers` - Numery Kolejkowe (Kompatybilność Wsteczna)

**Opis:** Tabela numerów kolejkowych (zachowana dla kompatybilności wstecznej).

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator numeru | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `room_id` | INTEGER | ID sali | FK do `queue_rooms(id)`, wymagane |
| `number` | INTEGER | Numer kolejkowy | Wymagany |
| `status` | TEXT | Status numeru | **Wartości:** `waiting`, `current`, `completed`, `cancelled` |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |
| `called_at` | DATETIME | Czas wywołania | Opcjonalne |
| `completed_at` | DATETIME | Czas zakończenia | Opcjonalne |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `room_id` → `queue_rooms(id)`

---

## Tabele Systemu Importu

### 11. `import_settings` - Ustawienia Importu

**Opis:** Konfiguracja importu danych z zewnętrznych systemów.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator ustawienia | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `system_name` | VARCHAR(100) | Nazwa systemu zewnętrznego | Wymagana (np. 'igabinet', 'medinet') |
| `sync_code` | VARCHAR(16) UNIQUE | 16-znakowy kod synchronizacji | Unikalny, wymagany |
| `is_active` | INTEGER | Czy import aktywny | **Wartości:** `0` (nieaktywny), `1` (aktywny) |
| `api_endpoint` | VARCHAR(255) | URL endpointu API | Opcjonalne |
| `api_credentials` | TEXT | Dane logowania (JSON) | Opcjonalne |
| `last_sync` | DATETIME | Data ostatniej synchronizacji | Opcjonalne |
| `sync_frequency` | INTEGER | Częstotliwość synchronizacji (sekundy) | Domyślnie: 3600 |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |
| `updated_at` | DATETIME | Data ostatniej aktualizacji | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- UNIQUE na `sync_code`
- UNIQUE na `client_id, system_name`

---

### 12. `external_doctor_mappings` - Mapowania Lekarzy Zewnętrznych

**Opis:** Mapowania lekarzy z systemów zewnętrznych na lekarzy w systemie.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator mapowania | Auto-increment |
| `import_setting_id` | INTEGER | ID ustawienia importu | FK do `import_settings(id)`, wymagane |
| `external_doctor_id` | VARCHAR(100) | ID lekarza w systemie zewnętrznym | Wymagane |
| `external_doctor_name` | VARCHAR(200) | Nazwa lekarza w systemie zewnętrznym | Wymagane |
| `external_doctor_specialization` | VARCHAR(200) | Specjalizacja w systemie zewnętrznym | Opcjonalne |
| `system_doctor_id` | INTEGER | ID lekarza w systemie | FK do `queue_doctors(id)`, opcjonalne |
| `is_mapped` | INTEGER | Czy lekarz jest zmapowany | **Wartości:** `0` (niezmapowany), `1` (zmapowany) |
| `last_seen` | DATETIME | Ostatni raz widziany w systemie zewnętrznym | Opcjonalne |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `import_setting_id` → `import_settings(id)`
- FK na `system_doctor_id` → `queue_doctors(id)`
- UNIQUE na `import_setting_id, external_doctor_id`

---

### 13. `sync_logs` - Logi Synchronizacji

**Opis:** Logi operacji synchronizacji z systemami zewnętrznymi.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator logu | Auto-increment |
| `import_setting_id` | INTEGER | ID ustawienia importu | FK do `import_settings(id)`, wymagane |
| `sync_type` | VARCHAR(50) | Typ synchronizacji | **Wartości:** `full` (pełna), `incremental` (przyrostowa), `manual` (ręczna) |
| `status` | VARCHAR(20) | Status synchronizacji | **Wartości:** `success` (sukces), `error` (błąd), `partial` (częściowy) |
| `records_processed` | INTEGER | Liczba przetworzonych rekordów | Domyślnie: 0 |
| `records_updated` | INTEGER | Liczba zaktualizowanych rekordów | Domyślnie: 0 |
| `records_created` | INTEGER | Liczba utworzonych rekordów | Domyślnie: 0 |
| `error_message` | TEXT | Komunikat błędu | Opcjonalne |
| `started_at` | DATETIME | Czas rozpoczęcia | Domyślnie: CURRENT_TIMESTAMP |
| `completed_at` | DATETIME | Czas zakończenia | Opcjonalne |

**Indeksy:**
- FK na `import_setting_id` → `import_settings(id)`

---

## Tabele Pomocnicze

### 14. `campaign_assignments` - Przypisania Kampanii

**Opis:** Przypisania kampanii reklamowych do klientów.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator przypisania | Auto-increment |
| `campaign_id` | INTEGER | ID kampanii | FK do `campaigns(id)`, wymagane |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `is_accepted` | INTEGER | Czy klient zaakceptował kampanię | **Wartości:** `0` (niezaakceptowana), `1` (zaakceptowana) |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `campaign_id` → `campaigns(id)`
- FK na `client_id` → `users(id)`

---

### 15. `campaign_auto_accept` - Automatyczna Akceptacja Kategorii

**Opis:** Ustawienia automatycznej akceptacji kampanii z określonych kategorii.

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `category_id` | INTEGER | ID kategorii | FK do `categories(id)`, wymagane |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `category_id` → `categories(id)`
- UNIQUE na `client_id, category_id`

---

### 16. `csv_doctor_mappings` - Mapowania Lekarzy z CSV

**Opis:** Mapowania lekarzy z plików CSV (starsza funkcjonalność).

| Kolumna | Typ | Opis | Wartości |
|---------|-----|------|----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator mapowania | Auto-increment |
| `client_id` | INTEGER | ID klienta | FK do `users(id)`, wymagane |
| `csv_doctor_name` | VARCHAR(200) | Nazwa lekarza z pliku CSV | Wymagana |
| `system_doctor_id` | INTEGER | ID lekarza w systemie | FK do `queue_doctors(id)`, wymagane |
| `created_at` | DATETIME | Data utworzenia | Domyślnie: CURRENT_TIMESTAMP |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `system_doctor_id` → `queue_doctors(id)`

---

## Relacje Między Tabelami

### Główne Relacje:

1. **users** → **campaigns** (1:N) - Jeden użytkownik może mieć wiele kampanii
2. **users** → **client_displays** (1:N) - Jeden klient może mieć wiele wyświetlaczy
3. **users** → **queue_doctors** (1:N) - Jeden klient może mieć wielu lekarzy
4. **users** → **queue_rooms** (1:N) - Jeden klient może mieć wiele sal
5. **users** → **queue_appointments** (1:N) - Jeden klient może mieć wiele wizyt
6. **queue_doctors** → **queue_appointments** (1:N) - Jeden lekarz może mieć wiele wizyt
7. **queue_rooms** → **queue_doctors** (1:N) - Jedna sala może być przypisana do wielu lekarzy
8. **campaigns** → **ad_views** (1:N) - Jedna kampania może mieć wiele wyświetleń
9. **import_settings** → **external_doctor_mappings** (1:N) - Jedno ustawienie importu może mieć wiele mapowań
10. **import_settings** → **sync_logs** (1:N) - Jedno ustawienie importu może mieć wiele logów

---

## Indeksy Wydajnościowe

### Indeksy Unikalne:
- `users.username` - Unikalność nazw użytkowników
- `users.email` - Unikalność adresów email
- `queue_doctors.access_code` - Unikalność kodów dostępu lekarzy
- `client_displays.display_code` - Unikalność kodów wyświetlaczy
- `import_settings.sync_code` - Unikalność kodów synchronizacji
- `queue_appointments.tracking_code` - Unikalność kodów śledzenia wizyt

### Indeksy Wydajnościowe:
- `queue_appointments(doctor_id, appointment_date)` - Szybkie wyszukiwanie wizyt po lekarzu i dacie
- `queue_appointments(client_id, appointment_date)` - Szybkie wyszukiwanie wizyt po kliencie i dacie
- `ad_views(timestamp)` - Szybkie wyszukiwanie wyświetleń po czasie
- `external_doctor_mappings(import_setting_id, external_doctor_id)` - Szybkie wyszukiwanie mapowań

---

## Migracje Bazy Danych

System zawiera następujące migracje:

1. **add_tracking_code_index.php** - Dodaje indeks unikalności dla `tracking_code` w `queue_appointments`
2. **remove_room_id_from_appointments.php** - Usuwa kolumnę `room_id` z `queue_appointments` (wizyty przypisane tylko do lekarzy)
3. **update_appointment_status.php** - Aktualizuje statusy wizyt z `completed`/`cancelled` na `closed`

---

## Uwagi Techniczne

### Format Danych:
- **Daty:** Format SQLite DATETIME (YYYY-MM-DD HH:MM:SS)
- **Czas UTC:** Czas w systemie importu zapisywany w formacie UTC ISO 8601
- **Kody:** Wszystkie kody (tracking_code, sync_code, access_code) są generowane automatycznie
- **Hasła:** Hashowane przy użyciu `password_hash()` z PASSWORD_DEFAULT

### Ograniczenia:
- SQLite nie obsługuje DROP COLUMN bezpośrednio - wymagane jest tworzenie nowej tabeli
- Maksymalna długość VARCHAR jest ograniczona przez SQLite
- Indeksy unikalne z warunkami WHERE są obsługiwane w SQLite 3.8.0+

### Bezpieczeństwo:
- Wszystkie hasła są hashowane
- Dane API credentials przechowywane jako JSON w kolumnie TEXT
- Kody synchronizacji są unikalne i trudne do odgadnięcia
- Indeksy unikalne zapewniają integralność danych

---

*Dokumentacja wygenerowana na podstawie analizy kodu aplikacji KtoOstatni.pl v1*
