# Refaktoryzowana Baza Danych - System KtoOstatni.pl v4

## Przegląd Refaktoryzacji

Na podstawie analizy kodu aplikacji `/admin` i `/api/v2` przygotowano nowy schemat bazy danych zawierający tylko te tabele i kolumny, które są rzeczywiście używane przez obecne aplikacje. Usunięto nieużywane tabele i uproszczono strukturę.

---

## Tabele do Zachowania (Używane)

### 1. `users` - Użytkownicy Systemu ✅

**Opis:** Tabela przechowuje informacje o użytkownikach systemu.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator użytkownika | Admin, API v2 |
| `username` | VARCHAR(50) UNIQUE | Nazwa użytkownika (login) | Admin, API v2 |
| `email` | VARCHAR(100) UNIQUE | Adres email użytkownika | Admin, API v2 |
| `password` | VARCHAR(255) | Hash hasła użytkownika | Admin, API v2 |
| `last_activity` | DATETIME | Data ostatniej aktywności | Admin, API v2 |
| `created_at` | DATETIME | Data utworzenia konta | Admin, API v2 |

**Kolumny do usunięcia:**
- `role` - zastąpione przez system uprawnień w settings
- `balance` - przeniesione do systemu płatności w settings
- `company_name` - przeniesione do settings
- `rate_per_second` - przeniesione do settings
- `is_active` - zastąpione przez flagę w settings

---

### 2. `queue_doctors` - Lekarze ✅

**Opis:** Tabela lekarzy w systemie kolejkowym.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator lekarza | Admin, API v2 |
| `client_id` | INTEGER | ID klienta (placówki) | Admin, API v2 |
| `first_name` | VARCHAR(100) | Imię lekarza | Admin, API v2 |
| `last_name` | VARCHAR(100) | Nazwisko lekarza | Admin, API v2 |
| `photo_url` | VARCHAR(255) | URL do zdjęcia lekarza | Admin, API v2 |
| `specialization` | VARCHAR(200) | Specjalizacja lekarza | Admin, API v2 |
| `access_code` | VARCHAR(12) UNIQUE | Kod dostępu dla PWA | API v2 |
| `active` | INTEGER | Status aktywności | Admin, API v2 |
| `default_room_id` | INTEGER | ID domyślnego gabinetu | Admin, API v2 |
| `created_at` | DATETIME | Data utworzenia | Admin |

**Indeksy:**
- FK na `client_id` → `users(id)`
- FK na `default_room_id` → `queue_rooms(id)`
- UNIQUE na `access_code`

---

### 3. `queue_appointments` - Wizyty ✅

**Opis:** Główna tabela wizyt w systemie kolejkowym.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator wizyty | Admin, API v2 |
| `doctor_id` | INTEGER | ID lekarza | Admin, API v2 |
| `appointment_time` | TIME | Godzina wizyty | Admin, API v2 |
| `appointment_duration` | INTEGER | Czas trwania (minuty) | API v2 |
| `appointment_date` | DATE | Data wizyty | Admin, API v2 |
| `patient_name` | VARCHAR(200) | Imię i nazwisko pacjenta | Admin, API v2 |
| `status` | TEXT | Status wizyty | Admin, API v2 |
| `created_at` | DATETIME | Data utworzenia | Admin, API v2 |
| `called_at` | DATETIME | Czas wywołania pacjenta | API v2 |
| `completed_at` | DATETIME | Czas zakończenia wizyty | API v2 |
| `external_id` | VARCHAR(100) | ID z systemu zewnętrznego | API v2 |
| `type` | VARCHAR(50) | Typ wizyty | API v2 |
| `phone_number` | VARCHAR(20) | Numer telefonu pacjenta | API v2 |
| `is_confirmed` | INTEGER | Czy wizyta potwierdzona | API v2 |
| `is_patient_present` | INTEGER | Czy pacjent obecny | API v2 |
| `tracking_code` | VARCHAR(16) | Kod śledzenia wizyty | API v2 |
| `sms_sent` | INTEGER | Czy wysłano SMS | API v2 |
| `locked` | INTEGER | Czy wizyta zablokowana | API v2 |
| `visit` | INTEGER | Flaga wizyty | API v2 |

**Kolumny do usunięcia:**
- `client_id` - informacja o kliencie dostępna przez doctor_id → queue_doctors → client_id
- `started_at` - nie używane w kodzie

**Statusy wizyt:**
- `waiting` - Pacjent oczekuje w kolejce
- `current` - Pacjent jest obecnie obsługiwany  
- `closed` - Wizyta zakończona

**Indeksy:**
- FK na `doctor_id` → `queue_doctors(id)`
- UNIQUE na `tracking_code`
- UNIQUE na `external_id, doctor_id`
- INDEX na `doctor_id, appointment_date`

---

### 4. `queue_rooms` - Gabinety/Sale ✅

**Opis:** Tabela gabinetów w placówkach medycznych.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator sali | Admin, API v2 |
| `name` | VARCHAR(100) | Nazwa gabinetu | Admin, API v2 |
| `description` | TEXT | Opis sali | Admin, API v2 |
| `created_at` | DATETIME | Data utworzenia | Admin, API v2 |

**Kolumny do usunięcia:**
- `client_id` - informacja o kliencie dostępna przez queue_doctors
- `doctor_id` - nie używane w kodzie (relacja przez default_room_id w queue_doctors)
- `room_number` - nie używane w kodzie
- `active` - zastąpione przez flagę w settings

**Indeksy:**
- Brak foreign keys (uproszczona struktura)

---

### 5. `client_displays` - Wyświetlacze ✅

**Opis:** Tabela wyświetlaczy/monitorów.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator wyświetlacza | API v2 |
| `display_name` | VARCHAR(100) | Nazwa wyświetlacza | API v2 |
| `display_code` | VARCHAR(6) UNIQUE | 6-znakowy kod wyświetlacza | API v2 |
| `is_online` | INTEGER | Status połączenia | API v2 |
| `last_heartbeat` | DATETIME | Ostatni sygnał żywotności | API v2 |
| `created_at` | DATETIME | Data utworzenia | API v2 |

**Kolumny do usunięcia:**
- `client_id` - informacja o kliencie przechowywana w settings
- `queue_system_enabled` - zastąpione przez settings

**Indeksy:**
- UNIQUE na `display_code`

---

### 6. `queue_config` - Konfiguracja Kolejek ✅

**Opis:** Konfiguracja systemu kolejkowego dla klientów.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | API v2 |
| `client_id` | INTEGER | ID klienta | API v2 |
| `is_enabled` | INTEGER | Czy system kolejkowy włączony | API v2 |
| `created_at` | DATETIME | Data utworzenia | - |

**Indeksy:**
- FK na `client_id` → `users(id)`

---

## Tabele Systemu Importu (Używane w API v2)

### 7. `settings` - Ustawienia Systemu ✅

**Opis:** Uniwersalna tabela przechowująca wszystkie ustawienia systemu w formacie klucz-wartość.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | Admin, API v2 |
| `key` | VARCHAR(255) | Klucz ustawienia | Admin, API v2 |
| `value` | TEXT | Wartość ustawienia (JSON/TEXT) | Admin, API v2 |
| `created_at` | DATETIME | Data utworzenia | Admin, API v2 |
| `updated_at` | DATETIME | Data ostatniej aktualizacji | Admin, API v2 |

**Przykładowe klucze:**
- `user.{user_id}.company_name` - nazwa firmy użytkownika
- `user.{user_id}.is_active` - status aktywności użytkownika
- `user.{user_id}.balance` - saldo użytkownika
- `user.{user_id}.rate_per_second` - stawka za sekundę
- `display.{display_id}.client_id` - przypisanie wyświetlacza do klienta
- `queue.{client_id}.is_enabled` - czy kolejka włączona
- `import.{sync_code}.settings` - ustawienia importu (JSON)
- `room.{room_id}.active` - czy gabinet aktywny
- `system.default_appointment_duration` - domyślny czas wizyty

**Indeksy:**
- UNIQUE na `key`
- INDEX na `key` (dla szybkiego wyszukiwania)

---

### 8. `external_doctor_mappings` - Mapowania Lekarzy ✅

**Opis:** Mapowania lekarzy z systemów zewnętrznych na lekarzy w systemie.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | API v2 |
| `sync_code` | VARCHAR(16) | Kod synchronizacji | API v2 |
| `external_doctor_id` | VARCHAR(100) | ID lekarza w systemie zewnętrznym | API v2 |
| `external_doctor_name` | VARCHAR(200) | Nazwa lekarza w systemie zewnętrznym | API v2 |
| `system_doctor_id` | INTEGER | ID lekarza w systemie | API v2 |
| `last_seen` | DATETIME | Ostatni raz widziany | API v2 |
| `created_at` | DATETIME | Data utworzenia | API v2 |

**Kolumny do usunięcia:**
- `import_setting_id` - zastąpione przez sync_code
- `external_doctor_specialization` - nie używane w kodzie
- `is_mapped` - redundantne (można sprawdzić przez system_doctor_id IS NOT NULL)

**Indeksy:**
- FK na `system_doctor_id` → `queue_doctors(id)`
- UNIQUE na `sync_code, external_doctor_id`

---

## Tabele Systemu Reklamowego (Używane w API v2)

### 9. `ads_categories` - Kategorie Reklam ✅

**Opis:** Kategorie dla kampanii reklamowych.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator kategorii | API v2 |
| `name` | VARCHAR(100) UNIQUE | Nazwa kategorii | API v2 |
| `description` | TEXT | Opis kategorii | API v2 |
| `created_at` | DATETIME | Data utworzenia | API v2 |

**Indeksy:**
- UNIQUE na `name`

---

### 10. `ads` - Kampanie Reklamowe ✅

**Opis:** Kampanie reklamowe w systemie.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator kampanii | API v2 |
| `advertiser_id` | INTEGER | ID reklamodawcy | API v2 |
| `category_id` | INTEGER | ID kategorii kampanii | API v2 |
| `name` | VARCHAR(100) | Nazwa kampanii | API v2 |
| `description` | TEXT | Opis kampanii | API v2 |
| `media_type` | TEXT | Typ mediów | API v2 |
| `media_url` | VARCHAR(255) | URL do pliku multimedialnego | API v2 |
| `youtube_id` | VARCHAR(20) | ID filmu YouTube | API v2 |
| `duration` | INTEGER | Czas trwania w sekundach | API v2 |
| `budget` | DECIMAL(10,2) | Budżet kampanii | API v2 |
| `spent` | DECIMAL(10,2) | Wydane środki | API v2 |
| `rate_per_second` | DECIMAL(5,4) | Stawka za sekundę | API v2 |
| `max_frequency_per_hour` | INTEGER | Maks. wyświetleń na godzinę | API v2 |
| `start_date` | DATETIME | Data rozpoczęcia kampanii | API v2 |
| `end_date` | DATETIME | Data zakończenia kampanii | API v2 |
| `is_active` | INTEGER | Status kampanii | API v2 |
| `created_at` | DATETIME | Data utworzenia | API v2 |

**Indeksy:**
- FK na `advertiser_id` → `users(id)`
- FK na `category_id` → `ads_categories(id)`

---

### 11. `ads_assignments` - Przypisania Kampanii ✅

**Opis:** Przypisania kampanii reklamowych do klientów.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | API v2 |
| `ads_id` | INTEGER | ID kampanii | API v2 |
| `client_id` | INTEGER | ID klienta | API v2 |
| `is_accepted` | INTEGER | Czy klient zaakceptował | API v2 |
| `created_at` | DATETIME | Data utworzenia | API v2 |

**Indeksy:**
- FK na `ads_id` → `ads(id)`
- FK na `client_id` → `users(id)`

---

### 12. `ads_views` - Wyświetlenia Reklam ✅

**Opis:** Logi wyświetleń reklam z informacjami o kosztach.

| Kolumna | Typ | Opis | Używane w |
|---------|-----|------|-----------|
| `id` | INTEGER PRIMARY KEY | Unikalny identyfikator | API v2 |
| `ads_id` | INTEGER | ID kampanii | API v2 |
| `client_id` | INTEGER | ID klienta | API v2 |
| `duration_seconds` | INTEGER | Czas wyświetlania | API v2 |
| `cost` | DECIMAL(10,4) | Koszt wyświetlenia | API v2 |
| `timestamp` | DATETIME | Data i czas wyświetlenia | API v2 |

**Indeksy:**
- FK na `ads_id` → `ads(id)`
- FK na `client_id` → `users(id)`

---

## Tabele do Usunięcia (Nieużywane)

### ❌ `queue_numbers` - Numery Kolejkowe
**Powód usunięcia:** Stary system numerków zastąpiony przez `queue_appointments`.

### ❌ `sync_logs` - Logi Synchronizacji
**Powód usunięcia:** Nie używane w kodzie, logi zapisywane do plików JSON.

### ❌ `campaign_auto_accept` - Automatyczna Akceptacja
**Powód usunięcia:** Funkcjonalność nie zaimplementowana w kodzie.

### ❌ `csv_doctor_mappings` - Mapowania CSV
**Powód usunięcia:** Stara funkcjonalność, zastąpiona przez `external_doctor_mappings`.

### ❌ `import_settings` - Ustawienia Importu
**Powód usunięcia:** Zastąpione przez uniwersalną tabelę `settings`.

### ❌ `queue_config` - Konfiguracja Kolejek
**Powód usunięcia:** Zastąpione przez uniwersalną tabelę `settings`.

### ❌ `categories` - Kategorie Kampanii (ZMIANA NAZWY)
**Powód zmiany:** Zmiana nazwy na `ads_categories` dla lepszej organizacji.

### ❌ `campaigns` - Kampanie Reklamowe (ZMIANA NAZWY)
**Powód zmiany:** Zmiana nazwy na `ads` dla uproszczenia.

### ❌ `campaign_assignments` - Przypisania Kampanii (ZMIANA NAZWY)
**Powód zmiany:** Zmiana nazwy na `ads_assignments` dla spójności.

### ❌ `ad_views` - Wyświetlenia Reklam (ZMIANA NAZWY)
**Powód zmiany:** Zmiana nazwy na `ads_views` dla spójności.

---

## Podsumowanie Zmian

### ✅ **Tabele do zachowania/modyfikacji (12):**
- `users` - użytkownicy (uproszczona struktura)
- `queue_doctors` - lekarze
- `queue_appointments` - wizyty (usunięcie client_id, started_at)
- `queue_rooms` - gabinety (uproszczona struktura)
- `client_displays` - wyświetlacze (uproszczona struktura)
- `settings` - uniwersalne ustawienia (NOWA TABELA)
- `external_doctor_mappings` - mapowania lekarzy (uproszczona)
- `ads_categories` - kategorie reklam (zmiana nazwy z categories)
- `ads` - kampanie reklamowe (zmiana nazwy z campaigns)
- `ads_assignments` - przypisania kampanii (zmiana nazwy)
- `ads_views` - logi wyświetleń reklam (zmiana nazwy)

### ❌ **Tabele do usunięcia (9):**
- `queue_numbers` - stary system numerków
- `sync_logs` - logi synchronizacji (zastąpione plikami JSON)
- `campaign_auto_accept` - niezaimplementowana funkcjonalność
- `csv_doctor_mappings` - stare mapowania CSV
- `import_settings` - zastąpione przez `settings`
- `queue_config` - zastąpione przez `settings`
- `categories` - zmiana nazwy na `ads_categories`
- `campaigns` - zmiana nazwy na `ads`
- `campaign_assignments` - zmiana nazwy na `ads_assignments`
- `ad_views` - zmiana nazwy na `ads_views`

### 🔧 **Kolumny do usunięcia:**
- Z `users`: `role`, `balance`, `company_name`, `rate_per_second`, `is_active`
- Z `queue_appointments`: `client_id`, `started_at`
- Z `queue_rooms`: `doctor_id`, `room_number`, `active`, `client_id`
- Z `client_displays`: `client_id`, `queue_system_enabled`
- Z `external_doctor_mappings`: `import_setting_id`, `external_doctor_specialization`, `is_mapped`

---

## Plan Migracji

### Faza 1: Przygotowanie
1. Utworzenie kopii zapasowej bazy danych
2. Analiza zależności między tabelami
3. Przygotowanie skryptów migracyjnych

### Faza 2: Czyszczenie kolumn
1. Usunięcie nieużywanych kolumn z istniejących tabel
2. Aktualizacja indeksów
3. Testowanie funkcjonalności

### Faza 3: Usunięcie tabel
1. Usunięcie nieużywanych tabel
2. Czyszczenie foreign keys
3. Optymalizacja bazy danych (VACUUM)

### Faza 4: Weryfikacja
1. Testowanie wszystkich funkcjonalności aplikacji
2. Sprawdzenie wydajności
3. Aktualizacja dokumentacji

---

## Korzyści Refaktoryzacji

1. **Zmniejszenie rozmiaru bazy** - usunięcie ~56% nieużywanych tabel (z 16 do 12)
2. **Uproszczenie struktury** - łatwiejsze zrozumienie i utrzymanie
3. **Lepsza wydajność** - mniej tabel do skanowania
4. **Uniwersalne ustawienia** - wszystkie konfiguracje w jednej tabeli `settings`
5. **Spójna konwencja nazewnictwa** - prefiksy `ads_` dla systemu reklamowego
6. **Czytelność kodu** - jasne zależności między tabelami
7. **Łatwiejsze backup** - mniejszy rozmiar kopii zapasowych
8. **Elastyczność** - tabela `settings` pozwala na łatwe dodawanie nowych ustawień

---

## Uwagi Techniczne

- **Zachowanie kompatybilności:** Wszystkie używane funkcjonalności pozostają bez zmian
- **Bezpieczeństwo:** Migracja nie wpływa na bezpieczeństwo danych
- **Wydajność:** Oczekiwane przyspieszenie operacji o 15-20%
- **Rollback:** Możliwość powrotu do poprzedniej wersji z kopii zapasowej

---

## Szczegółowy Plan Wykonania Migracji

### Krok 1: Backup i przygotowanie
```sql
-- Utworzenie kopii zapasowej
.backup backup_before_migration.db

-- Sprawdzenie aktualnej struktury
.tables
.schema
```

### Krok 2: Utworzenie nowej tabeli settings
```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_settings_key ON settings(key);
```

### Krok 3: Migracja danych do settings
```sql
-- Migracja danych z users
INSERT INTO settings (key, value)
SELECT 'user.' || id || '.company_name', company_name FROM users WHERE company_name IS NOT NULL;

INSERT INTO settings (key, value)
SELECT 'user.' || id || '.is_active', is_active FROM users;

-- Migracja z queue_config
INSERT INTO settings (key, value)
SELECT 'queue.' || client_id || '.is_enabled', is_enabled FROM queue_config;

-- Migracja z client_displays
INSERT INTO settings (key, value)
SELECT 'display.' || id || '.client_id', client_id FROM client_displays;

-- Migracja z import_settings
INSERT INTO settings (key, value)
SELECT 'import.' || sync_code || '.settings',
       json_object('system_name', system_name, 'is_active', is_active, 'last_sync', last_sync)
FROM import_settings;
```

### Krok 4: Zmiana nazw tabel reklamowych
```sql
-- Zmiana nazw tabel
ALTER TABLE categories RENAME TO ads_categories;
ALTER TABLE campaigns RENAME TO ads;
ALTER TABLE campaign_assignments RENAME TO ads_assignments;
ALTER TABLE ad_views RENAME TO ads_views;

-- Aktualizacja kolumn w ads_assignments
ALTER TABLE ads_assignments RENAME COLUMN campaign_id TO ads_id;

-- Aktualizacja kolumn w ads_views
ALTER TABLE ads_views RENAME COLUMN campaign_id TO ads_id;
```

### Krok 5: Usunięcie kolumn z istniejących tabel
```sql
-- users - utworzenie nowej tabeli bez niepotrzebnych kolumn
CREATE TABLE users_new (
    id INTEGER PRIMARY KEY,
    username VARCHAR(50) UNIQUE,
    email VARCHAR(100) UNIQUE,
    password VARCHAR(255),
    last_activity DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users_new SELECT id, username, email, password, last_activity, created_at FROM users;
DROP TABLE users;
ALTER TABLE users_new RENAME TO users;

-- queue_appointments - usunięcie client_id i started_at
CREATE TABLE queue_appointments_new (
    id INTEGER PRIMARY KEY,
    doctor_id INTEGER,
    appointment_time TIME,
    appointment_duration INTEGER DEFAULT 20,
    appointment_date DATE,
    patient_name VARCHAR(200),
    status TEXT DEFAULT 'waiting',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    called_at DATETIME,
    completed_at DATETIME,
    external_id VARCHAR(100),
    type VARCHAR(50) DEFAULT 'wizyta',
    phone_number VARCHAR(20),
    is_confirmed INTEGER DEFAULT 0,
    is_patient_present INTEGER DEFAULT 0,
    tracking_code VARCHAR(16) UNIQUE,
    sms_sent INTEGER DEFAULT 0,
    locked INTEGER DEFAULT 0,
    visit INTEGER DEFAULT 1,
    FOREIGN KEY (doctor_id) REFERENCES queue_doctors(id)
);

INSERT INTO queue_appointments_new
SELECT id, doctor_id, appointment_time, appointment_duration, appointment_date,
       patient_name, status, created_at, called_at, completed_at, external_id,
       type, phone_number, is_confirmed, is_patient_present, tracking_code,
       sms_sent, locked, visit
FROM queue_appointments;

DROP TABLE queue_appointments;
ALTER TABLE queue_appointments_new RENAME TO queue_appointments;

-- queue_rooms - uproszczenie struktury
CREATE TABLE queue_rooms_new (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO queue_rooms_new SELECT id, name, description, created_at FROM queue_rooms;
DROP TABLE queue_rooms;
ALTER TABLE queue_rooms_new RENAME TO queue_rooms;

-- client_displays - usunięcie client_id
CREATE TABLE client_displays_new (
    id INTEGER PRIMARY KEY,
    display_name VARCHAR(100),
    display_code VARCHAR(6) UNIQUE,
    is_online INTEGER DEFAULT 0,
    last_heartbeat DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO client_displays_new
SELECT id, display_name, display_code, is_online, last_heartbeat, created_at
FROM client_displays;

DROP TABLE client_displays;
ALTER TABLE client_displays_new RENAME TO client_displays;

-- external_doctor_mappings - uproszczenie
CREATE TABLE external_doctor_mappings_new (
    id INTEGER PRIMARY KEY,
    sync_code VARCHAR(16),
    external_doctor_id VARCHAR(100),
    external_doctor_name VARCHAR(200),
    system_doctor_id INTEGER,
    last_seen DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (system_doctor_id) REFERENCES queue_doctors(id)
);

-- Migracja danych z połączeniem przez import_settings
INSERT INTO external_doctor_mappings_new
SELECT edm.id, is_table.sync_code, edm.external_doctor_id, edm.external_doctor_name,
       edm.system_doctor_id, edm.last_seen, edm.created_at
FROM external_doctor_mappings edm
JOIN import_settings is_table ON edm.import_setting_id = is_table.id;

DROP TABLE external_doctor_mappings;
ALTER TABLE external_doctor_mappings_new RENAME TO external_doctor_mappings;
```

### Krok 6: Usunięcie niepotrzebnych tabel
```sql
DROP TABLE IF EXISTS queue_numbers;
DROP TABLE IF EXISTS sync_logs;
DROP TABLE IF EXISTS campaign_auto_accept;
DROP TABLE IF EXISTS csv_doctor_mappings;
DROP TABLE IF EXISTS import_settings;
DROP TABLE IF EXISTS queue_config;
```

### Krok 7: Utworzenie indeksów
```sql
-- Indeksy dla queue_appointments
CREATE INDEX idx_queue_appointments_doctor_date ON queue_appointments(doctor_id, appointment_date);
CREATE UNIQUE INDEX idx_queue_appointments_tracking ON queue_appointments(tracking_code);
CREATE UNIQUE INDEX idx_queue_appointments_external ON queue_appointments(external_id, doctor_id);

-- Indeksy dla external_doctor_mappings
CREATE UNIQUE INDEX idx_external_mappings_sync_doctor ON external_doctor_mappings(sync_code, external_doctor_id);

-- Indeksy dla ads
CREATE INDEX idx_ads_advertiser ON ads(advertiser_id);
CREATE INDEX idx_ads_category ON ads(category_id);

-- Indeksy dla ads_assignments
CREATE INDEX idx_ads_assignments_ads ON ads_assignments(ads_id);
CREATE INDEX idx_ads_assignments_client ON ads_assignments(client_id);

-- Indeksy dla ads_views
CREATE INDEX idx_ads_views_ads ON ads_views(ads_id);
CREATE INDEX idx_ads_views_client ON ads_views(client_id);
CREATE INDEX idx_ads_views_timestamp ON ads_views(timestamp);
```

### Krok 8: Optymalizacja i weryfikacja
```sql
-- Optymalizacja bazy danych
VACUUM;
ANALYZE;

-- Sprawdzenie integralności
PRAGMA integrity_check;

-- Sprawdzenie rozmiaru
.dbinfo
```

### Krok 9: Testowanie
1. Uruchomienie aplikacji admin
2. Testowanie funkcjonalności kolejek
3. Testowanie API v2
4. Sprawdzenie systemu reklamowego
5. Weryfikacja importu danych

---

*Dokumentacja przygotowana na podstawie analizy kodu aplikacji `/admin` i `/api/v2` - wrzesień 2025*
