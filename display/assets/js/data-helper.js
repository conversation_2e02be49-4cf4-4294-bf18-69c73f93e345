// Funkcje pomocnicze do normalizacji danych w wyświetlaczu

class DataHelper {
    /**
     * Normalizuje imię i nazwisko - pierwsza litera wielka, reszta mała
     * Obsługuje polskie znaki diakryczne (ą, ć, ę, ł, ń, ó, ś, ź, ż)
     */
    static normalizeName(name) {
        if (!name || name.trim() === '') {
            return '';
        }
        
        // Usuń białe znaki z początku i końca
        const trimmedName = name.trim();
        
        // Podziel na słowa (spacje, myślniki, apostrofy)
        const words = trimmedName.split(/[\s\-]+/);
        
        const normalizedWords = words
            .filter(word => word.length > 0)
            .map(word => {
                // Konwertuj na małe litery
                const lowerWord = word.toLowerCase();
                
                // Pierwsza litera wielka z obsługą polskich znaków
                const firstChar = lowerWord.charAt(0);
                const restOfWord = lowerWord.slice(1);
                
                // Zamień pierwszą literę na wielką z obsługą polskich znaków
                const firstCharUpper = this.mbUcfirst(firstChar);
                
                return firstCharUpper + restOfWord;
            });
        
        return normalizedWords.join(' ');
    }
    
    /**
     * Zamienia pierwszą literę na wielką z obsługą polskich znaków diakrycznych
     */
    static mbUcfirst(string) {
        if (!string || string.length === 0) {
            return string;
        }
        
        // Mapa polskich znaków małych -> wielkich
        const polishChars = {
            'ą': 'Ą', 'ć': 'Ć', 'ę': 'Ę', 'ł': 'Ł',
            'ń': 'Ń', 'ó': 'Ó', 'ś': 'Ś', 'ź': 'Ź', 'ż': 'Ż'
        };
        
        const firstChar = string.charAt(0);
        
        // Sprawdź czy to polski znak diakryczny
        if (polishChars[firstChar]) {
            return polishChars[firstChar];
        }
        
        // Dla innych znaków użyj standardowej funkcji
        return firstChar.toUpperCase();
    }
    
    /**
     * Normalizuje pełne imię i nazwisko
     */
    static normalizeFullName(firstName, lastName) {
        const normalizedFirst = this.normalizeName(firstName);
        const normalizedLast = this.normalizeName(lastName);
        
        if (!normalizedFirst && !normalizedLast) {
            return '';
        }
        
        return `${normalizedFirst} ${normalizedLast}`.trim();
    }
    
    /**
     * Normalizuje specjalizację lekarza
     */
    static normalizeSpecialization(specialization) {
        if (!specialization || specialization.trim() === '') {
            return '';
        }
        
        return this.normalizeName(specialization);
    }
    
    /**
     * Normalizuje nazwę gabinetu/pokoju
     */
    static normalizeRoomName(roomName) {
        if (!roomName || roomName.trim() === '') {
            return '';
        }
        
        return this.normalizeName(roomName);
    }
}
