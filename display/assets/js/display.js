// Zmienne globalne
let currentAdIndex = 0;
let ytPlayer = null;
let heartbeatInterval = null;
let queueCheckInterval = null;
let queueData = null;
let currentRoomIndex = 0;
let roomRotationInterval = null;
let isVideoPlaying = false;
let videoPlaybackStarted = false;

// Obsługa trybu pełnoekranowego
const displayContainer = document.getElementById("displayContainer");
const fullscreenBtn = document.getElementById("fullscreenBtn");

if (fullscreenBtn) {
  fullscreenBtn.addEventListener("click", toggleFullscreen);
}

// Funkcja do przełączania trybu pełnoekranowego
function toggleFullscreen() {
  if (
    !document.fullscreenElement &&
    !document.mozFullScreenElement &&
    !document.webkitFullscreenElement &&
    !document.msFullscreenElement
  ) {
    // Przejście do trybu pełnoekranowego
    if (displayContainer.requestFullscreen) {
      displayContainer.requestFullscreen();
    } else if (displayContainer.mozRequestFullScreen) {
      displayContainer.mozRequestFullScreen();
    } else if (displayContainer.webkitRequestFullscreen) {
      displayContainer.webkitRequestFullscreen();
    } else if (displayContainer.msRequestFullscreen) {
      displayContainer.msRequestFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    }
  } else {
    // Wyjście z trybu pełnoekranowego
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    }
    if (fullscreenBtn) {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Nasłuchiwanie zmiany stanu pełnoekranowego
document.addEventListener("fullscreenchange", updateFullscreenButton);
document.addEventListener("webkitfullscreenchange", updateFullscreenButton);
document.addEventListener("mozfullscreenchange", updateFullscreenButton);
document.addEventListener("MSFullscreenChange", updateFullscreenButton);

function updateFullscreenButton() {
  if (fullscreenBtn) {
    if (
      document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement
    ) {
      fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
    } else {
      fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    }
  }
}

// Funkcje API
function sendHeartbeat() {
  fetch(`/api/v2/display/${display.display_code}/heartbeat`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    }
  });
}

function recordAdView(campaignId, durationSeconds) {
  fetch("/api/v2/ads/view", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      campaign_id: campaignId,
      client_id: display.client_id,
      duration: durationSeconds
    }),
  });
}

// Funkcja do sprawdzania i aktualizacji danych o kolejce
function checkQueueStatus() {
  console.log('checkQueueStatus: Sprawdzanie statusu kolejki...');
  console.log('checkQueueStatus: display.client_id =', display.client_id);

  // Jeśli nie ma jeszcze danych kolejki, pobierz je bezpośrednio
  if (!queueData) {
    console.log('checkQueueStatus: Brak danych kolejki, pobieram bezpośrednio...');
    fetch(`/api/v2/queue/${display.client_id}`)
      .then((response) => {
        console.log('checkQueueStatus: Odpowiedź HTTP dla queue (direct):', response.status);
        return response.json();
      })
      .then((response) => {
        console.log('checkQueueStatus: Otrzymano dane kolejki (direct):', response);
        processQueueData(response);
      })
      .catch((error) => {
        console.error("Błąd podczas pobierania danych o kolejce (direct):", error);
      });
    return;
  }

  // Sprawdź czy są zmiany w systemie
  fetch(`/api/v2/changes/${display.client_id}`)
    .then((response) => {
      console.log('checkQueueStatus: Odpowiedź HTTP dla changes:', response.status);
      return response.json();
    })
    .then((changesResponse) => {
      console.log('checkQueueStatus: Otrzymano dane o zmianach:', changesResponse);

      if (!changesResponse.success) {
        console.error('checkQueueStatus: Błąd API changes:', changesResponse.error);
        return;
      }

      // Jeśli nie ma zmian, nie pobieraj pełnych danych
      if (!changesResponse.hasChanges) {
        console.log('checkQueueStatus: Brak zmian w systemie, pomijam pobieranie danych');
        return;
      }

      console.log('checkQueueStatus: Wykryto zmiany, pobieram pełne dane...');

      // Pobierz pełne dane kolejki
      return fetch(`/api/v2/queue/${display.client_id}`);
    })
    .then((response) => {
      if (!response) return; // Jeśli nie było zmian, response będzie undefined
      
      console.log('checkQueueStatus: Odpowiedź HTTP dla queue:', response.status);
      return response.json();
    })
    .then((response) => {
      if (!response) return; // Jeśli nie było zmian, response będzie undefined
      
      console.log('checkQueueStatus: Otrzymano dane kolejki:', response);
      processQueueData(response);
    })
    .catch((error) => {
      console.error("Błąd podczas pobierania danych o kolejce:", error);
    });
}

// Funkcja do przetwarzania danych kolejki
function processQueueData(response) {
  if (!response.success) {
    console.error('processQueueData: Błąd API queue:', response.error);
    return;
  }

  const data = response.data || response;

  // Porównaj tylko istotne dane (bez timestamp)
  const newDataForComparison = {
    client_id: data.client_id,
    doctors: data.doctors
  };
  const oldDataForComparison = queueData ? {
    client_id: queueData.client_id,
    doctors: queueData.doctors
  } : null;

  const newDataStr = JSON.stringify(newDataForComparison);
  const oldDataStr = JSON.stringify(oldDataForComparison);

  console.log('processQueueData: Porównuję dane...');
  console.log('processQueueData: oldDataStr length:', oldDataStr ? oldDataStr.length : 0);
  console.log('processQueueData: newDataStr length:', newDataStr.length);

  if (newDataStr !== oldDataStr) {
    console.log('processQueueData: Dane się zmieniły, aktualizuję wyświetlacz');

    // Sprawdź czy zmieniła się aktualna wizyta u któregoś lekarza
    checkForCurrentAppointmentChanges(queueData, data);

    // Sprawdź czy zmieniła się lista lekarzy z aktualnymi wizytami
    const oldDoctorsWithCurrent = queueData ? queueData.doctors.filter(d => d.current).map(d => d.id) : [];
    const newDoctorsWithCurrent = data.doctors.filter(d => d.current).map(d => d.id);
    const doctorsListChanged = JSON.stringify(oldDoctorsWithCurrent.sort()) !== JSON.stringify(newDoctorsWithCurrent.sort());

    queueData = data;

    // Aktualizuj wyświetlacz tylko jeśli zmieniła się lista lekarzy z aktualnymi wizytami
    if (doctorsListChanged) {
      console.log('processQueueData: Lista lekarzy z aktualnymi wizytami się zmieniła, przebudowuję wyświetlacz');
      updateQueueDisplay();
    } else {
      console.log('processQueueData: Lista lekarzy się nie zmieniła, aktualizuję tylko zawartość');
      // Sprawdź czy zmieniły się szczegóły wizyt
      const oldCurrentAppointments = queueData ? queueData.doctors.filter(d => d.current).map(d => ({ id: d.id, current: d.current })) : [];
      const newCurrentAppointments = data.doctors.filter(d => d.current).map(d => ({ id: d.id, current: d.current }));

      console.log('processQueueData: Stare wizyty:', JSON.stringify(oldCurrentAppointments));
      console.log('processQueueData: Nowe wizyty:', JSON.stringify(newCurrentAppointments));

      updateCurrentAppointmentContent();
    }

    // Ustawienie rotacji lekarzy, jeśli jest więcej niż 1 lekarz z aktualną wizytą
    const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current);
    if (doctorsWithCurrent.length > 1) {
      if (!roomRotationInterval) {
        roomRotationInterval = setInterval(rotateRooms, 10000);
      }
    } else {
      if (roomRotationInterval) {
        clearInterval(roomRotationInterval);
        roomRotationInterval = null;
      }
    }
  } else {
    console.log('processQueueData: Dane się nie zmieniły');
  }
}



// Funkcja do rotacji lekarzy
function rotateRooms() {
  console.log('rotateRooms: Rozpoczynam rotację, currentRoomIndex =', currentRoomIndex);
  if (!queueData || !queueData.doctors) return;

  // Filtruj lekarzy z aktualną wizytą
  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current);
  console.log('rotateRooms: Liczba lekarzy z aktualnymi wizytami:', doctorsWithCurrent.length);
  if (doctorsWithCurrent.length <= 1) return;

  const currentRoomContainer = document.querySelector(
    `.queue-rooms-container.active`,
  );
  if (currentRoomContainer) {
    currentRoomContainer.classList.remove("active");
  }

  currentRoomIndex = (currentRoomIndex + 1) % doctorsWithCurrent.length;
  console.log('rotateRooms: Nowy currentRoomIndex =', currentRoomIndex);

  const nextRoomContainer = document.querySelector(
    `.queue-rooms-container[data-room-index="${currentRoomIndex}"]`,
  );
  if (nextRoomContainer) {
    nextRoomContainer.classList.add("active");
    console.log('rotateRooms: Aktywowano kontener lekarza o indeksie', currentRoomIndex);
  } else {
    console.log('rotateRooms: Nie znaleziono kontenera o indeksie', currentRoomIndex);
  }
}

// Funkcja do aktualizacji wyświetlania kolejki
function updateQueueDisplay() {
  console.log('updateQueueDisplay: Rozpoczynam aktualizację wyświetlacza kolejki', new Date().toISOString());
  console.log('updateQueueDisplay: queueData =', queueData);

  if (!queueData) {
    console.log('updateQueueDisplay: Brak queueData, przerywam');
    return;
  }

  console.log('updateQueueDisplay: Liczba lekarzy w kolejce:', queueData.doctors ? queueData.doctors.length : 'brak doctors');

  // Sprawdź, czy istnieje panel boczny kolejki, jeśli nie - utwórz go
  let queueSidebar = document.querySelector(".queue-sidebar");
  console.log('updateQueueDisplay: queueSidebar =', queueSidebar);

  if (!queueSidebar) {
    console.log('updateQueueDisplay: Tworzę nowy queueSidebar');
    const contentContainer = document.getElementById("contentContainer");
    console.log('updateQueueDisplay: contentContainer =', contentContainer);

    if (!contentContainer) {
      console.log('updateQueueDisplay: Brak contentContainer, przerywam');
      return;
    }

    // Przekształć kontener na układ z kolejką
    contentContainer.parentElement.classList.add("display-with-queue");

    // Utwórz panel boczny kolejki
    queueSidebar = document.createElement("div");
    queueSidebar.className = "queue-sidebar";
    contentContainer.parentElement.insertBefore(queueSidebar, contentContainer);

    // Zmień klasę kontenera reklam
    contentContainer.className = "queue-ad-container";
    console.log('updateQueueDisplay: queueSidebar utworzony');
  }

  console.log('updateQueueDisplay: Czyszczę zawartość queueSidebar');
  queueSidebar.innerHTML = "";

  // Filtruj lekarzy z aktualną wizytą
  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current);
  console.log('updateQueueDisplay: Lekarzy z aktualną wizytą:', doctorsWithCurrent.length);

  // Resetuj currentRoomIndex tylko jeśli przekracza liczbę dostępnych lekarzy
  if (currentRoomIndex >= doctorsWithCurrent.length) {
    currentRoomIndex = 0;
  }

  console.log('updateQueueDisplay: currentRoomIndex =', currentRoomIndex);

  doctorsWithCurrent.forEach((doctor, index) => {
    console.log('updateQueueDisplay: Przetwarzam lekarza:', doctor.name, 'index:', index);
    const room = doctor.room;
    const current = doctor.current;
    const waiting = doctor.waiting;

    console.log('updateQueueDisplay: Dodaję lekarza do wyświetlacza:', doctor.name);

    const doctorContainer = document.createElement("div");
    doctorContainer.className = `queue-rooms-container ${index === currentRoomIndex ? "active" : ""}`;
    doctorContainer.setAttribute("data-room-index", index);

    const roomElement = document.createElement("div");
    roomElement.className = "queue-room";

    // Nagłówek z informacjami o lekarzu
    const roomHeader = document.createElement("div");
    roomHeader.className = "queue-room-header";

    const doctorName = DataHelper.normalizeName(doctor.name) || 'Lekarz';
    const doctorPhoto = doctor.photo_url || null;

    roomHeader.innerHTML = `
                <div class="doctor-photo slide-in-top">
                    ${doctorPhoto ? `<img src="${doctorPhoto}" alt="Lekarz">` : '<i class="fas fa-user-md" style="font-size: 60px; color: #fff; line-height: 150px;"></i>'}
                </div>
                <div class="slide-in-bottom">
                    <div class="doctor-name">
                        ${doctorName}
                    </div>
                </div>
            `;
    roomElement.appendChild(roomHeader);

    // Aktualna wizyta lub następna jeśli nie ma aktualnej
    const currentElement = document.createElement("div");
    currentElement.className = "queue-current slide-in-left";

    let displayAppointment = current;
    let labelText = "Aktualnie:";

    // Jeśli nie ma aktualnej wizyty, weź pierwszą oczekującą
    if (!current && waiting && waiting.length > 0) {
      displayAppointment = waiting[0];
      labelText = "Następna:";
    }

    if (displayAppointment) {
      // Formatuj czas - wyciągnij tylko godzinę z appointment_time
      let time = displayAppointment.number || displayAppointment.appointment_time;
      if (time && time.includes(' ')) {
        // Jeśli appointment_time zawiera datę i czas, wyciągnij tylko czas
        time = time.split(' ')[1] || time;
      }
      
      const firstName = displayAppointment.patient_name ? displayAppointment.patient_name.split(" ")[0] : '';
      const patientName = firstName ? `p. ${DataHelper.normalizeName(firstName)}` : 'Pacjent';
      currentElement.innerHTML = `
                <div class="current-label">${labelText}</div>
                <div class="current-number">${time}</div>
                <div class="current-patient">${patientName}</div>
            `;
    } else {
      currentElement.innerHTML = `
                <div class="current-label">Aktualnie:</div>
                <div class="current-number">-</div>
                <div class="current-patient">Brak wizyty</div>
            `;
    }
    roomElement.appendChild(currentElement);

    // Oczekujące wizyty
    if (waiting && waiting.length > 0) {
      const waitingElement = document.createElement("div");
      waitingElement.className = "queue-waiting slide-in-right";
      waitingElement.innerHTML =
        '<div class="waiting-label">Następne wizyty:</div>';

      // Jeśli nie ma aktualnej wizyty, pomiń pierwszą oczekującą (już pokazana jako "Następna")
      // Ogranicz do maksymalnie 3 wizyt oczekujących
      const waitingToShow = (!current ? waiting.slice(1) : waiting).slice(0, 3);

      waitingToShow.forEach((appointment) => {
        const appointmentElement = document.createElement("div");
        appointmentElement.className = "queue-number";

        // Formatuj czas - wyciągnij tylko godzinę z appointment_time
        let time = appointment.number || appointment.appointment_time;
        if (time && time.includes(' ')) {
          // Jeśli appointment_time zawiera datę i czas, wyciągnij tylko czas
          time = time.split(' ')[1] || time;
        }
        
        const firstName = appointment.patient_name ? appointment.patient_name.split(" ")[0] : '';
        const patientName = firstName ? `p. ${DataHelper.normalizeName(firstName)}` : 'Pacjent';

        appointmentElement.innerHTML = `
                    <div class="number">${time}</div>
                    <div class="patient">${patientName}</div>
                `;
        waitingElement.appendChild(appointmentElement);
      });

      // Dodaj element tylko jeśli są wizyty do pokazania
      if (waitingToShow.length > 0) {
        roomElement.appendChild(waitingElement);
      }
    }

    doctorContainer.appendChild(roomElement);
    queueSidebar.appendChild(doctorContainer);
    console.log('updateQueueDisplay: Dodano lekarza do queueSidebar:', doctorName);
  });

  console.log('updateQueueDisplay: Zakończono aktualizację wyświetlacza kolejki');
}

// Funkcja wyświetlania reklam
function showNextAd() {
  if (campaigns.length === 0) {
    // Nie wyświetlaj żadnych komunikatów - system kolejkowy działa niezależnie
    return;
  }

  if (ytPlayer) {
    ytPlayer.stopVideo();
    ytPlayer = null;
  }

  const ad = campaigns[currentAdIndex];
  let adContainer;

  if (queueSystemEnabled) {
    const contentContainer = document.getElementById("contentContainer");

    if (!document.querySelector(".display-with-queue")) {
      // Formatuj datę jeśli jest dostępna
      let dateText = '';
      if (queueData && queueData.display_date) {
        const date = new Date(queueData.display_date);
        dateText = `<div class="queue-date">${date.toLocaleDateString('pl-PL', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })}</div>`;
      }

      contentContainer.innerHTML = `
                <div class="display-with-queue">
                    <div class="queue-sidebar">
                        <div class="queue-header">
                          System kolejkowy
                          ${dateText}
                        </div>
                    </div>
                    <div class="queue-ad-container" id="adDisplay"></div>
                </div>
            `;

      checkQueueStatus();
      queueCheckInterval = setInterval(checkQueueStatus, 5000);
    }

    adContainer = document.getElementById("adDisplay");
  } else {
    adContainer = document.getElementById("contentContainer");
  }

  adContainer.innerHTML = "";
  adContainer.removeAttribute("style");

  let adElement = null;
  let adDuration = ad.duration || 15;

  switch (ad.media_type) {
    case "image":
      adElement = document.createElement("img");
      adElement.className = "ad-image";
      adElement.src = ad.media_url;
      adElement.alt = ad.name;
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      const imageTimeout = setTimeout(function () {
        console.error("Timeout podczas ładowania obrazu:", ad.media_url);
        rotateAd();
      }, 5000);

      adElement.onerror = function () {
        clearTimeout(imageTimeout);
        console.error("Błąd podczas ładowania obrazu:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onload = function () {
        clearTimeout(imageTimeout);
        if (adElement.naturalWidth > adElement.naturalHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }
      };

      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(adElement);
      break;

    case "video":
      const videoContainer = document.createElement("div");
      videoContainer.className = "video-container";
      videoContainer.style.display = "flex";
      videoContainer.style.justifyContent = "center";
      videoContainer.style.alignItems = "center";

      adElement = document.createElement("video");
      adElement.className = "ad-video";
      adElement.src = ad.media_url;
      adElement.controls = false;
      adElement.autoplay = false;
      adElement.muted = false;
      adElement.preload = "auto";
      adElement.loop = false;
      adElement.playsInline = true;
      adElement.style.display = "block";
      adElement.style.margin = "auto";

      adElement.onerror = function () {
        console.error("Błąd podczas ładowania pliku wideo:", ad.media_url);
        setTimeout(function () {
          rotateAd();
        }, 1000);
      };

      adElement.onloadedmetadata = function () {
        if (adElement.videoWidth > adElement.videoHeight) {
          adElement.style.width = "calc(100vw - 300px)";
          adElement.style.height = "auto";
          adElement.style.maxHeight = "100vh";
        } else {
          adElement.style.height = "100vh";
          adElement.style.width = "auto";
          adElement.style.maxWidth = "calc(100vw - 300px)";
        }

        // Jeśli już rozpoczęto odtwarzanie wideo, automatycznie odtwórz
        if (videoPlaybackStarted) {
          adElement.currentTime = 0;
          adElement
            .play()
            .then(function () {
              adElement.addEventListener(
                "ended",
                function () {
                  console.log("[Video] Zakończono odtwarzanie video");
                  isVideoPlaying = false;
                  recordAdView(ad.id, adDuration);
                  rotateAd();
                },
                { once: true },
              );
            })
            .catch(function (error) {
              console.error(
                "Błąd podczas automatycznego odtwarzania wideo:",
                error,
              );
              setTimeout(function () {
                rotateAd();
              }, 1000);
            });
        }
      };

      // Tylko pierwsza reklama wymaga kliknięcia play
      if (!videoPlaybackStarted) {
        const playButton = document.createElement("div");
        playButton.className = "play-button";
        playButton.innerHTML = '<i class="fas fa-play"></i>';
        playButton.style.zIndex = "1000";

        playButton.addEventListener("click", function () {
          if (isVideoPlaying) return;

          isVideoPlaying = true;
          videoPlaybackStarted = true; // Oznacz że rozpoczęto odtwarzanie

          adElement.currentTime = 0;
          adElement
            .play()
            .then(function () {
              playButton.classList.add("hidden");

              adElement.addEventListener(
                "ended",
                function () {
                  console.log("[Video] Zakończono odtwarzanie video");
                  isVideoPlaying = false;
                  recordAdView(ad.id, adDuration);
                  rotateAd();
                },
                { once: true },
              );
            })
            .catch(function (error) {
              console.error("Błąd podczas odtwarzania wideo:", error);
              isVideoPlaying = false;
              setTimeout(function () {
                rotateAd();
              }, 1000);
            });
        });

        videoContainer.appendChild(playButton);
      }

      videoContainer.appendChild(adElement);
      adContainer.style.display = "flex";
      adContainer.style.justifyContent = "center";
      adContainer.style.alignItems = "center";
      adContainer.appendChild(videoContainer);
      break;

    case "youtube":
      const youtubeContainer = document.createElement("div");
      youtubeContainer.id = "youtubePlayer";
      youtubeContainer.className = "ad-youtube";
      adContainer.appendChild(youtubeContainer);

      ytPlayer = new YT.Player("youtubePlayer", {
        height: "100%",
        width: "100%",
        videoId: ad.youtube_id,
        playerVars: {
          autoplay: 1,
          controls: 0,
          mute: 1,
          rel: 0,
        },
        events: {
          onReady: function (event) {
            event.target.playVideo();
          },
          onStateChange: function (event) {
            if (event.data === YT.PlayerState.ENDED) {
              recordAdView(ad.id, adDuration);
              rotateAd();
            }
          },
          onError: function (event) {
            console.error(
              "Błąd podczas ładowania filmu YouTube:",
              ad.youtube_id,
            );
            setTimeout(function () {
              rotateAd();
            }, 1000);
          },
        },
      });
      break;

    default:
      adElement = document.createElement("div");
      adElement.className = "ad-container";
      adElement.innerHTML = `<h2>${ad.name}</h2><p>${ad.description}</p>`;
      adContainer.appendChild(adElement);
  }

  // Jeśli to nie jest YouTube (obsługiwany przez zdarzenia) lub wideo (obsługiwane przez onloadedmetadata)
  if (ad.media_type !== "youtube" && ad.media_type !== "video") {
    setTimeout(function () {
      recordAdView(ad.id, adDuration);
      rotateAd();
    }, adDuration * 1000);
  }
}

function rotateAd() {
  if (campaigns.length === 0) return;

  currentAdIndex = (currentAdIndex + 1) % campaigns.length;
  showNextAd();
}

// Funkcja do aktualizacji zawartości wizyt bez przebudowywania wyświetlacza
function updateCurrentAppointmentContent() {
  console.log('updateCurrentAppointmentContent: Aktualizuję zawartość wizyt');
  if (!queueData || !queueData.doctors) return;

  const doctorsWithCurrent = queueData.doctors.filter(doctor => doctor.current);
  console.log('updateCurrentAppointmentContent: Lekarzy z aktualnymi wizytami:', doctorsWithCurrent.length);

  doctorsWithCurrent.forEach((doctor, index) => {
    const doctorContainer = document.querySelector(`.queue-rooms-container[data-room-index="${index}"]`);
    if (!doctorContainer) {
      console.log('updateCurrentAppointmentContent: Nie znaleziono kontenera dla indeksu', index);
      return;
    }

    console.log('updateCurrentAppointmentContent: Aktualizuję lekarza:', doctor.name);

    // Aktualizuj informacje o aktualnej wizycie
    const currentPatientElement = doctorContainer.querySelector('.current-patient');
    const currentNumberElement = doctorContainer.querySelector('.current-number');

    if (currentPatientElement && doctor.current) {
      const firstName = doctor.current.patient_name.split(" ")[0];
      const patientName = `p. ${DataHelper.normalizeName(firstName)}`;
      currentPatientElement.textContent = patientName;
      console.log('updateCurrentAppointmentContent: Zaktualizowano pacjenta na:', patientName);
    }
    if (currentNumberElement && doctor.current) {
      currentNumberElement.textContent = doctor.current.number;
      console.log('updateCurrentAppointmentContent: Zaktualizowano numer na:', doctor.current.number);
    }

    // Aktualizuj listę oczekujących - znajdź kontener z oczekującymi
    const waitingElement = doctorContainer.querySelector('.queue-waiting');
    if (waitingElement && doctor.waiting) {
      // Usuń stare numery oczekujących (zachowaj label)
      const oldNumbers = waitingElement.querySelectorAll('.queue-number');
      oldNumbers.forEach(el => el.remove());

      // Dodaj nowe numery oczekujących
      doctor.waiting.forEach((appointment) => {
        const appointmentElement = document.createElement("div");
        appointmentElement.className = "queue-number";

        const time = appointment.number || appointment.appointment_time;
        const firstName = appointment.patient_name.split(" ")[0];
        const patientName = `p. ${DataHelper.normalizeName(firstName)}`;

        appointmentElement.innerHTML = `
          <span class="number">${time}</span>
          <span class="patient">${patientName}</span>
        `;
        waitingElement.appendChild(appointmentElement);
      });
      console.log('updateCurrentAppointmentContent: Zaktualizowano listę oczekujących');
    }
  });
}

// Funkcje powiadomień o zmianie wizyty
function checkForCurrentAppointmentChanges(oldData, newData) {
  if (!oldData || !oldData.doctors || !newData || !newData.doctors) {
    return;
  }

  // Sprawdź każdego lekarza w nowych danych
  newData.doctors.forEach(newDoctor => {
    if (!newDoctor.current) return;

    // Znajdź tego samego lekarza w starych danych
    const oldDoctor = oldData.doctors.find(d => d.id === newDoctor.id);

    if (!oldDoctor || !oldDoctor.current) {
      // Nowy lekarz z aktualną wizytą lub lekarz który wcześniej nie miał aktualnej wizyty
      showAppointmentNotification(newDoctor);
    } else if (oldDoctor.current.id !== newDoctor.current.id) {
      // Ten sam lekarz, ale inna wizyta
      showAppointmentNotification(newDoctor);
    }
  });
}

function showAppointmentNotification(doctorData) {
  const notification = document.getElementById('appointmentNotification');
  const doctorElement = document.getElementById('notificationDoctor');
  const patientElement = document.getElementById('notificationPatient');
  const timeElement = document.getElementById('notificationTime');

  if (!notification || !doctorData.current) return;

  // Funkcja do formatowania imienia pacjenta (p. Imię)
  function formatPatientName(fullName) {
    if (!fullName) return 'Nieznany pacjent';

    const parts = fullName.trim().split(' ');
    if (parts.length === 0) return 'Nieznany pacjent';

    const firstName = parts[0];
    return `p. ${DataHelper.normalizeName(firstName)}`;
  }

  // Wypełnij dane powiadomienia
  const doctorName = DataHelper.normalizeName(doctorData.name) || 'Lekarz';
  doctorElement.textContent = doctorName;
  patientElement.textContent = formatPatientName(doctorData.current.patient_name);
  timeElement.textContent = doctorData.current.number;

  // Pokaż powiadomienie
  notification.classList.add('show');

  // Ukryj powiadomienie po 5 sekundach
  setTimeout(() => {
    hideAppointmentNotification();
  }, 5000);
}

function hideAppointmentNotification() {
  const notification = document.getElementById('appointmentNotification');
  if (notification) {
    notification.classList.remove('show');
  }
}

// Inicjalizacja
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOMContentLoaded: Inicjalizacja wyświetlacza");
  console.log("DOMContentLoaded: display =", display);
  console.log("DOMContentLoaded: queueSystemEnabled =", queueSystemEnabled);
  console.log("DOMContentLoaded: campaigns.length =", campaigns.length);

  // Zawsze inicjalizuj system kolejkowy
  console.log("DOMContentLoaded: Wywołuję checkQueueStatus");
  checkQueueStatus();
  console.log("DOMContentLoaded: Ustawiam interval dla checkQueueStatus");
  queueCheckInterval = setInterval(checkQueueStatus, 5000);

  // Pokaż reklamy tylko jeśli są dostępne
  if (campaigns.length > 0) {
    showNextAd();
  } else {
    // Jeśli nie ma reklam, przygotuj interfejs dla samego systemu kolejkowego
    const contentContainer = document.getElementById("contentContainer");
    if (contentContainer) {
      contentContainer.parentElement.classList.add("display-with-queue");

      // Utwórz panel boczny kolejki
      const queueSidebar = document.createElement("div");
      queueSidebar.className = "queue-sidebar";
      contentContainer.parentElement.insertBefore(
        queueSidebar,
        contentContainer,
      );

      // Zmień klasę kontenera treści
      contentContainer.className = "queue-ad-container";

      // Pusty kontener bez dodatkowych komunikatów
      contentContainer.innerHTML = "";
    }
  }

  sendHeartbeat();
  setInterval(sendHeartbeat, 60000);
});

// Funkcja dla YouTube API
function onYouTubeIframeAPIReady() {
  if (
    campaigns.length > 0 &&
    campaigns[currentAdIndex].media_type === "youtube"
  ) {
    showNextAd();
  }
}
