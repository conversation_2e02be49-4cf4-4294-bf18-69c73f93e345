<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parowanie wyświetlacza - <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .pairing-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .pairing-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        
        .pairing-code {
            font-size: 4rem;
            font-weight: bold;
            color: #667eea;
            letter-spacing: 0.5rem;
            margin: 2rem 0;
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 15px;
            border: 3px dashed #667eea;
        }
        
        .icon-large {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .countdown {
            font-size: 1.2rem;
            color: #dc3545;
            font-weight: bold;
        }
        
        .instructions {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            text-align: left;
        }
        
        .step-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="pairing-container">
        <div class="pairing-card">
            <div class="pulse">
                <span class="material-icons-outlined icon-large">tv</span>
            </div>
            
            <h1 class="h2 mb-3">Parowanie wyświetlacza</h1>
            <p class="text-muted mb-4">Wyświetlacz: <strong><?= htmlspecialchars($display['display_name']) ?></strong></p>
            
            <div class="pairing-code pulse">
                <?= strtoupper($display['pairing_code']) ?>
            </div>
            
            <div class="countdown" id="countdown">
                Kod wygasa za: <span id="time-left"></span>
            </div>
            
            <div class="instructions">
                <h5 class="mb-3">Jak sparować wyświetlacz:</h5>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div>Przejdź do panelu administracyjnego</div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div>Kliknij zakładkę "Wyświetlacze"</div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div>Wpisz kod: <strong><?= strtoupper($display['pairing_code']) ?></strong></div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div>Kliknij "Sparuj wyświetlacz"</div>
                </div>
            </div>
            
            <div class="mt-4">
                <small class="text-muted">
                    <span class="material-icons-outlined" style="font-size: 1rem; vertical-align: middle;">info</span>
                    Kod automatycznie odświeży się po wygaśnięciu
                </small>
            </div>
        </div>
    </div>

    <script>
        // Countdown timer
        const expiresAt = new Date('<?= $display['pairing_expires_at'] ?>').getTime();
        
        function updateCountdown() {
            const now = new Date().getTime();
            const distance = expiresAt - now;
            
            if (distance < 0) {
                // Kod wygasł - odśwież stronę
                location.reload();
                return;
            }
            
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('time-left').textContent = 
                minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
        }
        
        // Aktualizuj co sekundę
        updateCountdown();
        setInterval(updateCountdown, 1000);
        
        // Sprawdzaj status parowania co 5 sekund
        setInterval(function() {
            fetch(window.location.href + '?check_pairing=1')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'paired') {
                        // Wyświetlacz został sparowany - przekieruj
                        location.reload();
                    }
                })
                .catch(error => {
                    // Ignoruj błędy - może być problem z siecią
                });
        }, 5000);
    </script>
</body>
</html>
