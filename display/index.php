<?php

/**
 * Display Application Entry Point
 * Obsługuje wyświetlacze systemu kolejkowego i reklam
 */

// Załaduj konfigurację
if (!defined('KTOOSTATNI_APP')) {
    define('KTOOSTATNI_APP', true);
}
require_once __DIR__ . '/../config.php';

// Debug
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Display: Uruchamianie aplikacji wyświetlacza");
    error_log("Display: REQUEST_URI: " . $_SERVER['REQUEST_URI']);
}

// Pobierz kod wyświetlacza z URL
$request_uri = $_SERVER['REQUEST_URI'];

// Usuń /display/ lub /wyswietlacz/ z początku URI
$displayCode = null;
if (strpos($request_uri, '/display/') === 0) {
    $displayCode = substr($request_uri, 9);
} elseif (strpos($request_uri, '/wyswietlacz/') === 0) {
    $displayCode = substr($request_uri, 13);
} elseif (strpos($request_uri, '/display') === 0 || strpos($request_uri, '/wyswietlacz') === 0) {
    // Sprawdź czy to jest /display lub /display?parametry
    $path_without_params = strtok($request_uri, '?');
    if ($path_without_params === '/display' || $path_without_params === '/wyswietlacz') {
        // Nowy wyświetlacz - brak kodu, pokaż instrukcję parowania
        $displayCode = null;
    } else {
        // Nieprawidłowy URL
        http_response_code(404);
        echo "Nieprawidłowy adres wyświetlacza";
        exit;
    }
} else {
    // Nieprawidłowy URL
    http_response_code(404);
    echo "Nieprawidłowy adres wyświetlacza";
    exit;
}

// Jeśli jest kod, usuń parametry GET
if ($displayCode !== null) {
    $displayCode = strtok($displayCode, '?');
    $displayCode = trim($displayCode, '/');

    if (empty($displayCode)) {
        $displayCode = null;
    }
}

if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Display: Kod wyświetlacza: " . $displayCode);
}

// Dodatkowy autoloader dla klas display (config.php już ma autoloader)
spl_autoload_register(function ($className) {
    $paths = [
        __DIR__ . '/controllers/' . $className . '.php',
        __DIR__ . '/models/' . $className . '.php'
    ];

    foreach ($paths as $path) {
        if (file_exists($path)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Display: Loading class $className from $path");
            }
            require_once $path;
            return;
        }
    }
});

// Inicjalizacja bazy danych - Database::getInstance() automatycznie inicjalizuje

// Utworzenie kontrolera wyświetlacza
$controller = new PublicDisplayController();
$controller->show($displayCode);
