{"name": "fidry/cpu-core-counter", "description": "Tiny utility to get the number of CPU cores.", "license": "MIT", "type": "library", "keywords": ["cpu", "core"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "autoload-dev": {"psr-4": {"Fidry\\CpuCoreCounter\\Test\\": "tests/"}}, "config": {"allow-plugins": {"ergebnis/composer-normalize": true, "infection/extension-installer": true, "phpstan/extension-installer": true}, "sort-packages": true}}