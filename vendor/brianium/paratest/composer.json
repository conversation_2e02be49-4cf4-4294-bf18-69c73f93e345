{"name": "brianium/paratest", "description": "Parallel testing for PHP", "license": "MIT", "type": "library", "keywords": ["testing", "PHPUnit", "concurrent", "parallel"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "homepage": "https://github.com/paratestphp/paratest", "funding": [{"type": "github", "url": "https://github.com/sponsors/Slamdunk"}, {"type": "paypal", "url": "https://paypal.me/filippotessarotto"}], "require": {"php": "~8.2.0 || ~8.3.0 || ~8.4.0", "ext-dom": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-simplexml": "*", "fidry/cpu-core-counter": "^1.2.0", "jean85/pretty-package-versions": "^2.0.6", "phpunit/php-code-coverage": "^10.1.16", "phpunit/php-file-iterator": "^4.1.0", "phpunit/php-timer": "^6.0.0", "phpunit/phpunit": "^10.5.36", "sebastian/environment": "^6.1.0", "symfony/console": "^6.4.7 || ^7.1.5", "symfony/process": "^6.4.7 || ^7.1.5"}, "require-dev": {"ext-pcov": "*", "ext-posix": "*", "doctrine/coding-standard": "^12.0.0", "phpstan/phpstan": "^1.12.6", "phpstan/phpstan-deprecation-rules": "^1.2.1", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.1", "squizlabs/php_codesniffer": "^3.10.3", "symfony/filesystem": "^6.4.3 || ^7.1.5"}, "autoload": {"psr-4": {"ParaTest\\": ["src/"]}}, "autoload-dev": {"psr-4": {"ParaTest\\Tests\\": "test/"}}, "bin": ["bin/paratest", "bin/paratest_for_phpstorm"], "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "infection/extension-installer": true}, "sort-packages": true}}