# ChangeLog

All notable changes are documented in this file using the [Keep a CHANGELOG](https://keepachangelog.com/) principles.

## [5.1.3] - 2025-09-22

### Changed

* Suppress `not representable as an int, cast occurred` warning triggered on PHP 8.5

## [5.1.2] - 2024-03-02

### Changed

* Do not use implicitly nullable parameters

## [5.1.1] - 2023-09-24

### Changed

* [#52](https://github.com/sebastianbergmann/exporter/pull/52): Optimize export of large arrays and object graphs

## [5.1.0] - 2023-09-18

### Changed

* [#51](https://github.com/sebastianbergmann/exporter/pull/51): Export arrays using short array syntax

[5.1.3]: https://github.com/sebastianbergmann/exporter/compare/5.1.2...5.1.3
[5.1.2]: https://github.com/sebastianbergmann/exporter/compare/5.1.1...5.1.2
[5.1.1]: https://github.com/sebastianbergmann/exporter/compare/5.1.0...5.1.1
[5.1.0]: https://github.com/sebastianbergmann/exporter/compare/5.0.1...5.1.0
