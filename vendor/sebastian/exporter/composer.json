{"name": "sebastian/exporter", "description": "Provides the functionality to export PHP variables for visualization", "keywords": ["exporter", "export"], "homepage": "https://www.github.com/sebastianbergmann/exporter", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy"}, "config": {"platform": {"php": "8.1.0"}, "optimize-autoloader": true, "sort-packages": true}, "prefer-stable": true, "require": {"php": ">=8.1", "ext-mbstring": "*", "sebastian/recursion-context": "^5.0"}, "require-dev": {"phpunit/phpunit": "^10.5"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture"]}, "extra": {"branch-alias": {"dev-main": "5.1-dev"}}}