{"name": "sebastian/object-reflector", "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "config": {"platform": {"php": "8.1.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=8.1"}, "require-dev": {"phpunit/phpunit": "^10.0"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "extra": {"branch-alias": {"dev-main": "3.0-dev"}}}