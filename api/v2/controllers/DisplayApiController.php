<?php

class DisplayApiController extends ApiController {

    /**
     * <PERSON>bierz dane dla wyświetlacza
     */
    public function getDisplayData($code) {
        try {
            // Znajdź wyświetlacz po kodzie (uproszczone - bez company_name i queue_config)
            $stmt = $this->db->prepare("
                SELECT d.id, d.display_name as name, d.display_code,
                       d.is_online, d.last_heartbeat, d.created_at
                FROM client_displays d
                WHERE d.display_code = ? AND d.is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found or inactive', 404);
            }

            $response = [
                'display' => [
                    'id' => $display['id'],
                    'name' => $display['name'],
                    'code' => $display['display_code']
                ],
                'queue' => null,
                'ads' => [],
                'timestamp' => date('c')
            ];

            // Pobierz dane kolejki - z<PERSON><PERSON> włączone (uproszczone)
            $response['queue'] = $this->getQueueData();

            // Pobierz reklamy - zawsze włączone (Config::isModuleEnabled usunięte)
            $response['ads'] = $this->getAdsData();

            $this->success($response);
        } catch (Exception $e) {
            $this->log('Error getting display data: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to get display data', 500);
        }
    }

    /**
     * Heartbeat wyświetlacza
     */
    public function heartbeat($code) {
        try {
            // Znajdź wyświetlacz
            $stmt = $this->db->prepare("
                SELECT id FROM client_displays
                WHERE display_code = ? AND is_online >= 0
            ");
            $stmt->execute([$code]);
            $display = $stmt->fetch();

            if (!$display) {
                $this->error('Display not found', 404);
            }

            // Zaktualizuj ostatni heartbeat i ustaw status online
            $stmt = $this->db->prepare("
                UPDATE client_displays
                SET last_heartbeat = datetime('now'), is_online = 1
                WHERE id = ?
            ");
            $stmt->execute([$display['id']]);

            $this->success([
                'heartbeat_recorded' => true,
                'timestamp' => date('c')
            ]);
        } catch (Exception $e) {
            $this->log('Error recording heartbeat: ' . $e->getMessage(), 'ERROR');
            $this->error('Failed to record heartbeat', 500);
        }
    }

    /**
     * Pobierz dane kolejki dla wyświetlacza (uproszczone - bez client_id)
     */
    private function getQueueData() {
        try {
            // Pobierz sale (uproszczone - bez doctor_id)
            $stmt = $this->db->prepare("
                SELECT r.*
                FROM queue_rooms r
                ORDER BY r.name
            ");
            $stmt->execute();
            $rooms = $stmt->fetchAll();

            $roomsData = [];
            foreach ($rooms as $room) {
                // Pobierz aktualną wizytę dla lekarzy przypisanych do tego gabinetu
                $stmt = $this->db->prepare("
                    SELECT qa.* FROM queue_appointments qa
                    JOIN queue_doctors qd ON qa.doctor_id = qd.id
                    WHERE qd.default_room_id = ? AND qa.status = 'current'
                    ORDER BY qa.id DESC LIMIT 1
                ");
                $stmt->execute([$room['id']]);
                $current = $stmt->fetch();

                // Sprawdź wizyty na kolejne dni jeśli nie ma na dzisiaj
                $waiting = [];
                $dateToCheck = date('Y-m-d'); // Dzisiaj
                $maxDays = 3; // Sprawdź dzisiaj, jutro i pojutrze
                $daysChecked = 0;

                while (empty($waiting) && $daysChecked < $maxDays) {
                    // Pobierz kolejne numery dla danej daty
                    // Pokaż tylko wizyty ze statusem 'waiting' - bez filtrowania czasowego
                    // Logika czasowa jest teraz obsługiwana przez statusy
                    $stmt = $this->db->prepare("
                        SELECT qa.* FROM queue_appointments qa
                        JOIN queue_doctors qd ON qa.doctor_id = qd.id
                        WHERE qd.default_room_id = ? AND qa.status = 'waiting'
                        AND date(qa.appointment_date) = ?
                        ORDER BY qa.appointment_time ASC LIMIT 3
                    ");
                    $stmt->execute([$room['id'], $dateToCheck]);
                    $waiting = $stmt->fetchAll();

                    if (empty($waiting)) {
                        // Sprawdź następny dzień
                        $daysChecked++;
                        $nextDate = new DateTime($dateToCheck);
                        $nextDate->add(new DateInterval('P1D'));
                        $dateToCheck = $nextDate->format('Y-m-d');
                    } else {
                        // Znaleziono wizyty - przerwij pętlę
                        break;
                    }
                }

                $roomsData[] = [
                    'id' => $room['id'],
                    'name' => $room['name'],
                    'room_number' => null, // room_number usunięte z tabeli
                    'doctor' => null, // doctor info usunięte z tabeli rooms
                    'current' => $current ? [
                        'number' => $current['appointment_time'],
                        'patient_name' => $current['patient_name']
                    ] : null,
                    'waiting' => array_map(function ($appointment) {
                        return [
                            'number' => $appointment['appointment_time'],
                            'patient_name' => $appointment['patient_name']
                        ];
                    }, $waiting)
                ];
            }

            return [
                'enabled' => true,
                'rooms' => $roomsData
            ];
        } catch (Exception $e) {
            $this->log('Error getting queue data for display: ' . $e->getMessage(), 'ERROR');
            return ['enabled' => false, 'error' => 'Failed to load queue data'];
        }
    }

    /**
     * Pobierz dane reklam dla wyświetlacza
     * UWAGA: System reklamowy uproszczony - bez przypisań do klientów
     */
    private function getAdsData() {
        try {
            // Pobierz aktywną kampanię - bez sprawdzania przypisań (tabela ads_assignments usunięta)
            $stmt = $this->db->prepare("
                SELECT c.id, c.name, c.media_type, c.media_url, c.youtube_id,
                       c.duration, c.description
                FROM ads c
                WHERE 1=1 -- UWAGA: Kolumny is_active, budget, spent zostały usunięte z ads
                ORDER BY RANDOM()
                LIMIT 1
            ");

            $stmt->execute();
            $campaign = $stmt->fetch();

            if (!$campaign) {
                return [];
            }

            // Przygotuj URL mediów - uproszczone bez Config::getAppUrl()
            if ($campaign['media_type'] === 'image' || $campaign['media_type'] === 'video') {
                // Zakładamy względny URL
                $campaign['media_url'] = '/' . ltrim($campaign['media_url'], '/');
            }

            return [$campaign];
        } catch (Exception $e) {
            $this->log('Error getting ads data for display: ' . $e->getMessage(), 'ERROR');
            return [];
        }
    }
}
