<?php

namespace Tests;

use PHPUnit\Framework\TestCase as BaseTestCase;

/**
 * Bazowa klasa testowa dla testów funkcjonalnych i integracyjnych
 */
abstract class TestCase extends BaseTestCase {
    protected $db;
    protected $originalDbPath;

    protected function setUp(): void {
        parent::setUp();

        // Zapisz oryginalną ścieżkę bazy danych
        $this->originalDbPath = defined('DB_PATH') ? DB_PATH : null;

        // Ustaw testową bazę danych
        if (!defined('TEST_DB_PATH')) {
            define('TEST_DB_PATH', __DIR__ . '/database/test.db');
        }

        // Nadpisz stałą DB_PATH dla testów
        if (defined('DB_PATH')) {
            runkit7_constant_redefine('DB_PATH', TEST_DB_PATH);
        } else {
            define('DB_PATH', TEST_DB_PATH);
        }

        // Zresetuj bazę danych przed każdym testem
        $this->resetDatabase();

        // Inicjalizuj połączenie z bazą danych
        $this->initDatabase();

        // Załaduj fixtures
        $this->loadFixtures();
    }

    protected function tearDown(): void {
        // Przywróć oryginalną ścieżkę bazy danych
        if ($this->originalDbPath && function_exists('runkit7_constant_redefine')) {
            runkit7_constant_redefine('DB_PATH', $this->originalDbPath);
        }

        parent::tearDown();
    }

    /**
     * Resetowanie bazy danych
     */
    protected function resetDatabase(): void {
        if (function_exists('resetTestDatabase')) {
            resetTestDatabase();
        }
    }

    /**
     * Inicjalizacja połączenia z bazą danych
     */
    protected function initDatabase(): void {
        try {
            $this->db = new \PDO('sqlite:' . TEST_DB_PATH);
            $this->db->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);
            $this->db->setAttribute(\PDO::ATTR_DEFAULT_FETCH_MODE, \PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            $this->fail('Nie można połączyć się z testową bazą danych: ' . $e->getMessage());
        }
    }

    /**
     * Ładowanie fixtures
     */
    protected function loadFixtures(): void {
        // Podstawowe fixtures będą ładowane automatycznie
        $this->loadBasicFixtures();
    }

    /**
     * Ładowanie podstawowych fixtures
     */
    protected function loadBasicFixtures(): void {
        // Użytkownicy testowi
        $this->db->exec("
            INSERT INTO users (id, username, email, password, role) VALUES 
            (1, 'admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_DEFAULT) . "', 'admin'),
            (2, 'client', '<EMAIL>', '" . password_hash('client123', PASSWORD_DEFAULT) . "', 'client')
        ");

        // Gabinety testowe
        $this->db->exec("
            INSERT INTO queue_rooms (id, name, description) VALUES 
            (1, 'Gabinet 1', 'Pierwszy gabinet testowy'),
            (2, 'Gabinet 2', 'Drugi gabinet testowy')
        ");

        // Lekarze testowi
        $this->db->exec("
            INSERT INTO queue_doctors (id, client_id, first_name, last_name, specialization, access_code, active, default_room_id) VALUES 
            (1, 2, 'Jan', 'Kowalski', 'Kardiolog', '123456789012', 1, 1),
            (2, 2, 'Anna', 'Nowak', 'Dermatolog', '123456789013', 1, 2),
            (3, 2, 'Piotr', 'Wiśniewski', 'Neurolog', '123456789014', 0, 1)
        ");

        // Wizyty testowe
        $this->db->exec("
            INSERT INTO queue_appointments (id, doctor_id, appointment_time, appointment_date, patient_name, status, tracking_code) VALUES 
            (1, 1, '08:00', '" . date('Y-m-d') . "', 'Jan Testowy', 'waiting', 'TEST001'),
            (2, 1, '08:30', '" . date('Y-m-d') . "', 'Anna Testowa', 'current', 'TEST002'),
            (3, 2, '09:00', '" . date('Y-m-d') . "', 'Piotr Testowy', 'closed', 'TEST003')
        ");

        // Wyświetlacze testowe
        $this->db->exec("
            INSERT INTO client_displays (id, display_name, display_code, is_online) VALUES 
            (1, 'Wyświetlacz Główny', 'DISP01', 1),
            (2, 'Wyświetlacz Poczekalnia', 'DISP02', 0)
        ");

        // Reklamy testowe
        $this->db->exec("
            INSERT INTO ads (id, name, description, media_type, media_url, duration) VALUES 
            (1, 'Reklama Testowa 1', 'Opis reklamy testowej', 'image', '/uploads/ads/test1.jpg', 30),
            (2, 'Reklama YouTube', 'Reklama z YouTube', 'youtube', '', 60)
        ");
    }

    /**
     * Pomocnicza metoda do wykonywania żądań HTTP
     */
    protected function makeRequest(string $method, string $url, array $data = [], array $headers = []): array {
        // Symulacja żądania HTTP dla testów integracyjnych
        $method = strtoupper($method);

        // Ustaw zmienne $_SERVER dla routingu
        $_SERVER['REQUEST_METHOD'] = $method;
        $_SERVER['REQUEST_URI'] = $url;
        $_SERVER['HTTP_HOST'] = 'localhost';

        // Ustaw dane POST/PUT
        if (in_array($method, ['POST', 'PUT', 'PATCH']) && !empty($data)) {
            $_POST = $data;
        }

        // Rozpocznij buforowanie wyjścia
        ob_start();

        try {
            // Symuluj routing aplikacji
            if (strpos($url, '/api/v2') === 0) {
                // API v2
                require __DIR__ . '/../api/v2/index.php';
            } elseif (strpos($url, '/admin') === 0) {
                // Panel admina
                require __DIR__ . '/../index.php';
            }

            $output = ob_get_contents();
        } catch (\Exception $e) {
            $output = json_encode(['error' => true, 'message' => $e->getMessage()]);
        } finally {
            ob_end_clean();
        }

        // Spróbuj zdekodować JSON
        $decoded = json_decode($output, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decoded;
        }

        return ['raw_output' => $output];
    }

    /**
     * Pomocnicza metoda do logowania użytkownika w sesji
     */
    protected function loginUser(int $userId): void {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        $_SESSION['user_id'] = $userId;
        $_SESSION['login_time'] = time();
    }

    /**
     * Pomocnicza metoda do wylogowania użytkownika
     */
    protected function logoutUser(): void {
        if (session_status() !== PHP_SESSION_NONE) {
            session_unset();
            session_destroy();
        }
    }
}
